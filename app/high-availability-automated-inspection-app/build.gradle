apply plugin: 'org.springframework.boot'
ext {
    versions = [
            springBoot: '2.6.15', // 框架当前使用的版本
    ]
}

dependencies {
    implementation project(":rest:high-availability-inspection-rest")

    implementation 'com.cmpay:lemon-framework-starter-actuator-prometheus'
    implementation 'com.cmpay:lemon-framework-starter-actuator-security'
    implementation('com.cmpay:cmpay-tracing-starter')

    implementation("com.cmpay:lemon-framework-starter-cloud-openfeign")


    implementation 'com.github.xiaoymin:knife4j-openapi3-spring-boot-starter:4.5.0'
}

configurations.all {
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'org.springframework.boot') {
            details.useVersion versions.springBoot
        }
    }
}

springBoot {
    mainClass = 'com.cmpay.hacp.inspection.HighAvailabilityAutomatedInspectionApplication'
}
