-- 首页面板信息
-- ----------------------------
-- Table structure for dashboard_panels
-- ----------------------------
DROP TABLE IF EXISTS `dashboard_panels`;
CREATE TABLE `dashboard_panels`
(
	`workspace_id`  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL COMMENT '项目空间ID',
	`type`          varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL COMMENT '面板类型（PanelType）',
	`provider`      varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL COMMENT '面板提供者（GRAFANA）',
	`url`           varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '面板URL',
	`variables`     varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '面板变量',
	`extra_options` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '面板其他参数',
	`status`        tinyint(4)                                               NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`   datetime                                                 NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`   datetime                                                 NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`workspace_id`, `type`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '首页面板信息'
  ROW_FORMAT = Dynamic;
