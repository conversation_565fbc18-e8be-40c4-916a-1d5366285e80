INSERT INTO `inspection_tag` (`workspace_id`, `name`, `description`)
VALUES ('default', '性能监控', '用于性能监控的标签', 'admin', 'admin'),
       ('default', '安全检查', '用于安全检查的标签', 'admin', 'admin'),
       ('default', '配置审计', '用于配置审计的标签', 'admin', 'admin'),
       ('default', '日志分析', '用于日志分析的标签', 'admin', 'admin'),
       ('default', '资源监控', '用于资源监控的标签', 'admin', 'admin');

-- 插入机房配置数据
INSERT INTO `prometheus_zone_config` (`zone_code`, `zone_name`, `description`)
VALUES ('changsha', '长沙机房', '长沙数据中心'),
       ('zhezhong', '浙中机房', '浙中数据中心'),
       ('yidongyun', '移动云机房', '移动云数据中心'),
       ('zhuzhoujinke', '株洲金科机房', '株洲金科数据中心'),
       ('zhuzhouzhifu', '株洲支付机房', '株洲支付数据中心');

-- 插入Nginx指标定义
INSERT INTO `prometheus_indicator_definition` (`indicator_id`, `indicator_name`, `indicator_type`, `description`)
VALUES ('0000000501', 'middleware_nginx_readconnect', 'mid', 'Nginx读取连接数指标'),
       ('0000000511', 'middleware_nginx_state', 'mid', 'Nginx服务状态指标'),
       ('0000000521', 'middleware_nginx_request', 'mid', 'Nginx请求数指标'),
       ('0000000531', 'middleware_nginx_connect', 'mid', 'Nginx连接数量指标'),
       ('0000000541', 'middleware_nginx_awaitconnect', 'mid', 'Nginx等待连接数指标'),
       ('0000000551', 'middleware_nginx_writeconnect', 'mid', 'Nginx回写连接数指标');

-- 插入Prometheus指标定义
INSERT INTO `prometheus_indicator_definition` (`indicator_id`, `indicator_name`, `indicator_type`, `description`)
VALUES ('0000000832', 'middleware_prometheus_state', 'mid', 'Prometheus服务状态指标');

-- 插入Kafka指标定义
INSERT INTO `prometheus_indicator_definition` (`indicator_id`, `indicator_name`, `indicator_type`, `description`)
VALUES ('0000000646', 'middleware_kafka_lag', 'mid', 'Kafka每分钟消息积压数指标'),
       ('0000000619', 'middleware_kafka_state', 'mid', 'Kafka服务状态指标'),
       ('0000000629', 'middleware_kafka_produce', 'mid', 'Kafka每分钟消息产生速度指标'),
       ('0000000637', 'middleware_kafka_consumption', 'mid', 'Kafka每分钟消息消费速度指标'),
       ('0000000655', 'middleware_kafka_partitions', 'mid', 'Kafka topic分区数指标');

-- 插入RabbitMQ指标定义
INSERT INTO `prometheus_indicator_definition` (`indicator_id`, `indicator_name`, `indicator_type`, `description`)
VALUES ('0000000663', 'middleware_rabbitmq_disk_threshold', 'mid', 'RabbitMQ磁盘空间阈值指标'),
       ('0000000733', 'middleware_rabbitmq_disk_remain', 'mid', 'RabbitMQ磁盘剩余空间指标'),
       ('0000000723', 'middleware_rabbitmq_state', 'mid', 'RabbitMQ服务状态指标'),
       ('0000000746', 'middleware_rabbitmq_disk_alert', 'mid', 'RabbitMQ磁盘告警指标'),
       ('0000000675', 'middleware_rabbitmq_connect', 'mid', 'RabbitMQ连接数指标'),
       ('0000000759', 'middleware_rabbitmq_memory_alert', 'mid', 'RabbitMQ内存告警指标'),
       ('0000000682', 'middleware_rabbitmq_communication', 'mid', 'RabbitMQ信道数指标'),
       ('0000000689', 'middleware_rabbitmq_consumer', 'mid', 'RabbitMQ消费者数指标'),
       ('0000000696', 'middleware_rabbitmq_messages', 'mid', 'RabbitMQ消息积压数指标'),
       ('0000000712', 'middleware_rabbitmq_memory', 'mid', 'RabbitMQ内存使用率指标');

-- 插入Elasticsearch指标定义
INSERT INTO `prometheus_indicator_definition` (`indicator_id`, `indicator_name`, `indicator_type`, `description`)
VALUES ('0000000822', 'middleware_es_state', 'mid', 'Elasticsearch服务状态指标');

-- 插入Redis指标定义
INSERT INTO `prometheus_indicator_definition` (`indicator_id`, `indicator_name`, `indicator_type`, `description`)
VALUES ('0000000812', 'middleware_redis_master', 'mid', 'Redis主从同步指标'),
       ('0000000561', 'middleware_redis_state', 'mid', 'Redis服务状态指标'),
       ('0000000571', 'middleware_redis_memory', 'mid', 'Redis内存使用率指标'),
       ('0000000581', 'middleware_redis_connect', 'mid', 'Redis连接数指标'),
       ('0000000591', 'middleware_redis_refuse_connect', 'mid', 'Redis新建拒绝连接数指标'),
       ('0000000601', 'middleware_redis_execution', 'mid', 'Redis每秒执行指令指标'),
       ('0000000611', 'middleware_redis_master_abnormalities', 'mid', 'Redis主从同步异常指标');

-- 插入容器指标定义
INSERT INTO `prometheus_indicator_definition` (`indicator_id`, `indicator_name`, `indicator_type`, `description`)
VALUES ('0000006901', 'container_cpu_usage_pod_open', 'container', '容器CPU使用率(pod)-能开指标'),
       ('0000001062', 'container_cpu_usage_namespace', 'container', '容器CPU使用率(namespace)指标'),
       ('0000001039', 'container_info', 'container', '容器全量信息指标'),
       ('0000001111', 'container_storage_usage_namespace', 'container', '容器存储卷使用率(namespace)指标'),
       ('0000001081', 'container_memory_count', 'container', '容器内存总数(字节)指标'),
       ('0000001128', 'container_netflow_join_namespace', 'container', '容器网络流量-接入(namespace,pod)指标'),
       ('0000001145', 'container_netflow_goout_namespace', 'container', '容器网络流量-接出(namespace,pod)指标'),
       ('0000006916', 'container_memory_usage_pod_open', 'container', '容器内存使用率(pod)-能开指标'),
       ('0000001499', 'container_memory_usage_namespace', 'container', '容器内存使用率(namespace)指标'),
       ('0000001188', 'container_created_by_name', 'container', '容器资源名称指标'),
       ('0000001178', 'container_created_by_kind', 'container', '容器资源类型指标'),
       ('0000001169', 'container_namespace', 'container', '容器名称空间指标'),
       ('0000001053', 'container_cpu_count', 'container', '容器CPU总数指标'),
       ('0000001072', 'container_cpu_usage_workspace', 'container', '容器CPU使用率(workspace)指标'),
       ('0000001101', 'container_storage_count', 'container', '容器存储卷总数(字节)指标'),
       ('0000001199', 'container_pod', 'container', '容器pod名称指标');

-- 插入主机指标定义
INSERT INTO `prometheus_indicator_definition` (`indicator_id`, `indicator_name`, `indicator_type`, `description`)
VALUES ('1000000001', 'host_disk_usage_ip', 'host', '主机磁盘使用率指标(按IP)'),
       ('1000000002', 'host_disk_usage_appName', 'host', '主机磁盘使用率指标(按应用)'),
       ('1000000003', 'host_filesystem_avail_bytes', 'host', '主机文件系统可用字节指标'),
       ('1000000004', 'host_io_timeconsuming_appName', 'host', '主机IO耗时指标(按应用)'),
       ('1000000005', 'host_cpu_usage_appName', 'host', '主机CPU使用率指标(按应用)'),
       ('1000000006', 'host_swap_usage_ip', 'host', '主机Swap使用率指标'),
       ('1000000007', 'host_disk_writespeed_ip', 'host', '主机磁盘写速度指标'),
       ('1000000008', 'host_iops_read_ip', 'host', '主机读IOPS指标'),
       ('1000000009', 'host_memory_usage_ip', 'host', '主机内存使用率指标(按IP)'),
       ('1000000010', 'host_memory_usage_appName', 'host', '主机内存使用率指标(按应用)'),
       ('1000000011', 'host_cpu_usage_ip', 'host', '主机CPU使用率指标(按IP)'),
       ('1000000012', 'host_netflow_goout_appName', 'host', '主机网络出流量指标(按应用)'),
       ('1000000013', 'host_netflow_goout_ip', 'host', '主机网络出流量指标(按IP)'),
       ('1000000014', 'host_netflow_join_appName', 'host', '主机网络入流量指标(按应用)'),
       ('1000000015', 'host_netflow_join_ip', 'host', '主机网络入流量指标(按IP)'),
       ('1000000016', 'host_disk_gross_appName', 'host', '主机磁盘总量指标(按应用)'),
       ('1000000017', 'host_memory_gross_appName', 'host', '主机内存总量指标(按应用)'),
       ('1000000018', 'host_cpu_gross_appName', 'host', '主机CPU总量指标(按应用)'),
       ('1000000019', 'host_disk_readspeed_ip', 'host', '主机磁盘读速度指标'),
       ('1000000020', 'host_disk_size_ip', 'host', '主机磁盘大小指标'),
       ('1000000021', 'host_disk_inode_usage_ip', 'host', '主机磁盘Inode使用率指标'),
       ('1000000022', 'host_iops_write_ip', 'host', '主机写IOPS指标'),
       ('1000000023', 'host_io_timeconsuming_ip', 'host', '主机IO耗时指标(按IP)'),
       ('1000000024', 'host_tcp_conversation_ip', 'host', '主机TCP连接数指标'),
       ('1000000025', 'host_node_load15_ip', 'host', '主机15分钟负载指标'),
       ('1000000026', 'host_node_load5_ip', 'host', '主机5分钟负载指标'),
       ('1000000027', 'host_node_load1_ip', 'host', '主机1分钟负载指标'),
       ('1000000028', 'host_state', 'host', '主机状态指标'),
       ('1000000029', 'host_max_memory_usage_ip', 'host', '主机最大内存使用率指标'),
       ('1000000030', 'host_memory_size_ip', 'host', '主机内存大小指标'),
       ('1000000031', 'host_netflow_join_ip_appName', 'host', '主机网络入流量指标(按IP和应用)'),
       ('1000000032', 'host_netflow_goout_ip_appName', 'host', '主机网络出流量指标(按IP和应用)');

-- 插入Nginx指标参数配置
INSERT INTO `prometheus_indicator_param` (`indicator_id`, `param_order`, `param_name`, `param_code`, `is_required`,
                                          `description`)
VALUES
-- nginx读取连接数参数
('0000000501', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000501', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000501', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000501', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- nginx状态参数
('0000000511', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000511', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000511', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000511', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- nginx请求数参数
('0000000521', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000521', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000521', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000521', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- nginx连接数量参数
('0000000531', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000531', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000531', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000531', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- nginx等待连接数参数
('0000000541', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000541', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000541', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000541', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- nginx回写连接数参数
('0000000551', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000551', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000551', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000551', 4, 'zone', 'specialNameFour', 1, '机房区域');

-- 插入Prometheus指标参数配置
INSERT INTO `prometheus_indicator_param` (`indicator_id`, `param_order`, `param_name`, `param_code`, `is_required`,
                                          `description`)
VALUES ('0000000832', 1, 'appName', 'specialNameOne', 1, '应用名称'),
       ('0000000832', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
       ('0000000832', 3, 'job', 'specialNameThree', 1, '任务名称');

-- 插入Kafka指标参数配置
INSERT INTO `prometheus_indicator_param` (`indicator_id`, `param_order`, `param_name`, `param_code`, `is_required`,
                                          `description`)
VALUES
-- kafka每分钟消息积压数参数
('0000000646', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000646', 2, 'topic', 'specialNameTwo', 1, '主题名称'),
('0000000646', 3, 'consumergroup', 'specialNameThree', 1, '消费者组'),
-- kafka状态参数
('0000000619', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000619', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000619', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000619', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- kafka每分钟消息产生速度参数
('0000000629', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000629', 2, 'topic', 'specialNameTwo', 1, '主题名称'),
-- kafka每分钟消息消费速度参数
('0000000637', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000637', 2, 'topic', 'specialNameTwo', 1, '主题名称'),
('0000000637', 3, 'consumergroup', 'specialNameThree', 1, '消费者组'),
-- kafka topic分区数参数
('0000000655', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000655', 2, 'topic', 'specialNameTwo', 1, '主题名称');

-- 插入RabbitMQ指标参数配置
INSERT INTO `prometheus_indicator_param` (`indicator_id`, `param_order`, `param_name`, `param_code`, `is_required`,
                                          `description`)
VALUES
-- rabbitmq磁盘空间阈值参数
('0000000663', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000663', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000663', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000663', 4, 'zone', 'specialNameFour', 1, '机房区域'),
('0000000663', 5, 'cluster', 'specialNameFive', 1, '集群名称'),
('0000000663', 6, 'self', 'specialNameSix', 1, 'self标识'),
('0000000663', 7, 'node', 'specialNameSeven', 1, '节点名称'),
-- rabbitmq磁盘剩余空间参数
('0000000733', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000733', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000733', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000733', 4, 'zone', 'specialNameFour', 1, '机房区域'),
('0000000733', 5, 'cluster', 'specialNameFive', 1, '集群名称'),
('0000000733', 6, 'self', 'specialNameSix', 1, 'self标识'),
('0000000733', 7, 'node', 'specialNameSeven', 1, '节点名称'),
-- rabbitmq状态参数
('0000000723', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000723', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000723', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000723', 4, 'zone', 'specialNameFour', 1, '机房区域'),
('0000000723', 5, 'cluster', 'specialNameFive', 1, '集群名称'),
('0000000723', 6, 'node', 'specialNameSix', 1, '节点名称'),
-- rabbitmq磁盘告警参数
('0000000746', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000746', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000746', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000746', 4, 'zone', 'specialNameFour', 1, '机房区域'),
('0000000746', 5, 'cluster', 'specialNameFive', 1, '集群名称'),
('0000000746', 6, 'self', 'specialNameSix', 1, 'self标识'),
('0000000746', 7, 'node', 'specialNameSeven', 1, '节点名称'),
-- rabbitmq连接数参数
('0000000675', 1, 'appName', 'specialNameOne', 1, '应用名称'),
-- rabbitmq内存告警参数
('0000000759', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000759', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000759', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000759', 4, 'zone', 'specialNameFour', 1, '机房区域'),
('0000000759', 5, 'cluster', 'specialNameFive', 1, '集群名称'),
('0000000759', 6, 'self', 'specialNameSix', 1, 'self标识'),
('0000000759', 7, 'node', 'specialNameSeven', 1, '节点名称'),
-- rabbitmq信道数参数
('0000000682', 1, 'appName', 'specialNameOne', 1, '应用名称'),
-- rabbitmq消费者数参数
('0000000689', 1, 'appName', 'specialNameOne', 1, '应用名称'),
-- rabbitmq消息积压数参数
('0000000696', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000696', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000696', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000696', 4, 'zone', 'specialNameFour', 1, '机房区域'),
('0000000696', 5, 'cluster', 'specialNameFive', 1, '集群名称'),
('0000000696', 6, 'self', 'specialNameSix', 1, 'self标识'),
('0000000696', 7, 'durable', 'specialNameSeven', 1, '持久化标识'),
('0000000696', 8, 'queue', 'specialNameEight', 1, '队列名称'),
('0000000696', 9, 'vhost', 'specialNameNine', 1, '虚拟主机'),
('0000000696', 10, 'policy', 'specialNameTen', 1, '策略名称'),
-- rabbitmq内存使用率参数
('0000000712', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000712', 2, 'node', 'specialNameTwo', 1, '节点名称');

-- 插入Elasticsearch指标参数配置
INSERT INTO `prometheus_indicator_param` (`indicator_id`, `param_order`, `param_name`, `param_code`, `is_required`,
                                          `description`)
VALUES ('0000000822', 1, 'appName', 'specialNameOne', 1, '应用名称'),
       ('0000000822', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
       ('0000000822', 3, 'job', 'specialNameThree', 1, '任务名称'),
       ('0000000822', 4, 'zone', 'specialNameFour', 1, '机房区域');

-- 插入Redis指标参数配置
INSERT INTO `prometheus_indicator_param` (`indicator_id`, `param_order`, `param_name`, `param_code`, `is_required`,
                                          `description`)
VALUES
-- redis主从同步参数
('0000000812', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000812', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000812', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000812', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- redis状态参数
('0000000561', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000561', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000561', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000561', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- redis内存使用率参数
('0000000571', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000571', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000571', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000571', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- redis连接数参数
('0000000581', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000581', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000581', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000581', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- redis新建拒绝连接数参数
('0000000591', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000591', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000591', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000591', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- redis每秒执行指令参数
('0000000601', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000601', 2, 'instance', 'specialNameTwo', 1, '实例名称'),
('0000000601', 3, 'job', 'specialNameThree', 1, '任务名称'),
('0000000601', 4, 'zone', 'specialNameFour', 1, '机房区域'),
-- redis主从同步异常参数
('0000000611', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('0000000611', 2, 'instance', 'specialNameTwo', 1, '实例名称');

-- 插入容器指标参数配置
INSERT INTO `prometheus_indicator_param` (`indicator_id`, `param_order`, `param_name`, `param_code`, `is_required`,
                                          `description`)
VALUES
-- 容器cpu使用率(pod)-能开参数
('0000006901', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000006901', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000006901', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000006901', 4, 'pod', 'specialNameFour', 1, 'Pod名称'),
-- 容器cpu使用率(namespace)参数
('0000001062', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001062', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001062', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001062', 4, 'cloudName', 'specialNameFour', 1, '云平台名称'),
-- 容器全量信息参数
('0000001039', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001039', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001039', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001039', 4, 'pod', 'specialNameFour', 1, 'Pod名称'),
('0000001039', 5, 'created_by_kind', 'specialNameFive', 1, '创建资源类型'),
('0000001039', 6, 'created_by_name', 'specialNameSix', 1, '创建资源名称'),
('0000001039', 7, 'phase', 'specialNameSeven', 1, '阶段状态'),
('0000001039', 8, 'zone', 'specialNameEight', 1, '机房区域'),
-- 容器存储卷使用率(namespace)参数
('0000001111', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001111', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001111', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001111', 4, 'persistentvolumeclaim', 'specialNameFour', 1, '持久化存储卷'),
-- 容器内存总数(字节)参数
('0000001081', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001081', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001081', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001081', 4, 'pod', 'specialNameFour', 1, 'Pod名称'),
-- 容器网络流量-接入(namespace,pod)参数
('0000001128', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001128', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001128', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001128', 4, 'pod', 'specialNameFour', 1, 'Pod名称'),
-- 容器网络流量-接出(namespace,pod)参数
('0000001145', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001145', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001145', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001145', 4, 'pod', 'specialNameFour', 1, 'Pod名称'),
-- 容器内存使用率(pod)-能开参数
('0000006916', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000006916', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000006916', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000006916', 4, 'pod', 'specialNameFour', 1, 'Pod名称'),
-- 容器内存使用率(namespace)参数
('0000001499', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001499', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001499', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
-- 容器资源名称参数
('0000001188', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001188', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001188', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001188', 4, 'created_by_kind', 'specialNameFour', 1, '创建资源类型'),
('0000001188', 5, 'created_by_name', 'specialNameFive', 1, '创建资源名称'),
-- 容器资源类型参数
('0000001178', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001178', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001178', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001178', 4, 'created_by_kind', 'specialNameFour', 1, '创建资源类型'),
-- 容器名称空间参数
('0000001169', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001169', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001169', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
-- 容器cpu总数参数
('0000001053', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001053', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001053', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
-- 容器cpu使用率(workspace)参数
('0000001072', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001072', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001072', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
-- 容器存储卷总数(字节)参数
('0000001101', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001101', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001101', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001101', 4, 'persistentvolumeclaim', 'specialNameFour', 1, '持久化存储卷'),
-- 容器pod名称参数
('0000001199', 1, 'namespace', 'specialNameOne', 1, '命名空间'),
('0000001199', 2, 'k8splat', 'specialNameTwo', 1, 'K8S平台'),
('0000001199', 3, 'workspace', 'specialNameThree', 1, '工作空间'),
('0000001199', 4, 'created_by_kind', 'specialNameFour', 1, '创建资源类型'),
('0000001199', 5, 'created_by_name', 'specialNameFive', 1, '创建资源名称'),
('0000001199', 6, 'pod', 'specialNameSix', 1, 'Pod名称');

-- 插入主机指标参数配置
INSERT INTO `prometheus_indicator_param` (`indicator_id`, `param_order`, `param_name`, `param_code`, `is_required`,
                                          `description`)
VALUES
-- 主机磁盘使用率(IP)参数
('1000000001', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000001', 2, 'mountpoint', 'specialNameTwo', 1, '挂载点'),
-- 主机磁盘使用率(应用)参数
('1000000002', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000002', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
('1000000002', 3, 'device', 'specialNameThree', 1, '设备名称'),
-- 主机文件系统可用字节参数
('1000000003', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000003', 2, 'mountpoint', 'specialNameTwo', 1, '挂载点'),
('1000000003', 3, 'fstype', 'specialNameThree', 1, '文件系统类型'),
('1000000003', 4, 'instance', 'specialNameFour', 1, '实例名称'),
('1000000003', 5, 'ip', 'specialNameFive', 1, 'IP地址'),
('1000000003', 6, 'job', 'specialNameSix', 1, '任务名称'),
('1000000003', 7, 'device', 'specialNameSeven', 1, '设备名称'),
('1000000003', 8, 'secureDomain', 'specialNameEight', 1, '安全域'),
-- 主机IO耗时(应用)参数
('1000000004', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000004', 2, 'appName', 'specialNameTwo', 1, '应用名称'),
('1000000004', 3, 'device', 'specialNameThree', 1, '设备名称'),
('1000000004', 4, 'instance', 'specialNameFour', 1, '实例名称'),
('1000000004', 5, 'job', 'specialNameFive', 1, '任务名称'),
('1000000004', 6, 'zone', 'specialNameSix', 1, '机房区域'),
-- 主机CPU使用率(应用)参数
('1000000005', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000005', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
-- 主机Swap使用率参数
('1000000006', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000006', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
-- 主机磁盘写速度参数
('1000000007', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000007', 2, 'device', 'specialNameTwo', 1, '设备名称'),
-- 主机读IOPS参数
('1000000008', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000008', 2, 'device', 'specialNameTwo', 1, '设备名称'),
-- 主机内存使用率(IP)参数
('1000000009', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
-- 主机内存使用率(应用)参数
('1000000010', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000010', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
-- 主机CPU使用率(IP)参数
('1000000011', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
-- 主机网络出流量(应用)参数
('1000000012', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000012', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
-- 主机网络出流量(IP)参数
('1000000013', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
-- 主机网络入流量(应用)参数
('1000000014', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000014', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
-- 主机网络入流量(IP)参数
('1000000015', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
-- 主机磁盘总量(应用)参数
('1000000016', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000016', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
-- 主机内存总量(应用)参数
('1000000017', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000017', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
-- 主机CPU总量(应用)参数
('1000000018', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000018', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
-- 主机磁盘读速度参数
('1000000019', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000019', 2, 'device', 'specialNameTwo', 1, '设备名称'),
-- 主机磁盘大小参数
('1000000020', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000020', 2, 'mountpoint', 'specialNameTwo', 1, '挂载点'),
-- 主机磁盘Inode使用率参数
('1000000021', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000021', 2, 'mountpoint', 'specialNameTwo', 1, '挂载点'),
-- 主机写IOPS参数
('1000000022', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000022', 2, 'device', 'specialNameTwo', 1, '设备名称'),
-- 主机IO耗时(IP)参数
('1000000023', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000023', 2, 'device', 'specialNameTwo', 1, '设备名称'),
-- 主机TCP连接数参数
('1000000024', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
-- 主机15分钟负载参数
('1000000025', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000025', 2, 'appName', 'specialNameTwo', 1, '应用名称'),
-- 主机5分钟负载参数
('1000000026', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000026', 2, 'appName', 'specialNameTwo', 1, '应用名称'),
-- 主机1分钟负载参数
('1000000027', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000027', 2, 'appName', 'specialNameTwo', 1, '应用名称'),
-- 主机状态参数
('1000000028', 1, 'appName', 'specialNameOne', 1, '应用名称'),
('1000000028', 2, 'ip', 'specialNameTwo', 1, 'IP地址'),
('1000000028', 3, 'instance', 'specialNameThree', 1, '实例名称'),
('1000000028', 4, 'job', 'specialNameFour', 1, '任务名称'),
-- 主机最大内存使用率参数
('1000000029', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
-- 主机内存大小参数
('1000000030', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
-- 主机网络入流量(IP+应用)参数
('1000000031', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000031', 2, 'appName', 'specialNameTwo', 1, '应用名称'),
-- 主机网络出流量(IP+应用)参数
('1000000032', 1, 'ip', 'specialNameOne', 1, 'IP地址'),
('1000000032', 2, 'appName', 'specialNameTwo', 1, '应用名称');