package com.cmpay.hacp.extend.contaiiner.container;

import com.cmpay.hacp.emergency.bo.KubesphereMetaData;
import com.cmpay.hacp.extend.contaiiner.dto.EmergencyContainerAddReqDTO;
import com.cmpay.hacp.extend.contaiiner.dto.QueryPodsReqDto;
import com.cmpay.hacp.extend.container.bo.EmergencyContainerBO;
import com.cmpay.hacp.extend.container.service.impl.HacpKubesphereClient;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/8/29 16:57
 * @version 1.0
 */
@RestController
@RequestMapping("/v1/emergency/kubesphere")
@Api(tags = "Kubesphere管理")
public class KubesphereController {
    @Resource
    private HacpKubesphereClient kubesphereClient;


    @ApiOperation(value = "", notes = "查询流程实例列表")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/query-kubesphere-metaData")
    @LogNoneRecord
    public DefaultRspDTO<List<KubesphereMetaData>> queryKubesphereMetaData() {
        String workspaceId = TenantUtils.getWorkspaceIdNotNull();
        List<KubesphereMetaData> workspaces = kubesphereClient.queryClusters(workspaceId);
        workspaces.forEach(w -> {
            for(int i = 0, len = w.getItems().size(); i < len; i++){
                KubesphereMetaData item = w.getItems().get(i);
                List<KubesphereMetaData> nameSpaces = kubesphereClient.queryNameSpaces(workspaceId, item.getName());
                if (JudgeUtils.isEmpty(nameSpaces)) {
                    len--;
                    i--;
                    w.getItems().remove(item);
                } else {
                    item.setItems(nameSpaces);
                }
            }
        });
        return DefaultRspDTO.newSuccessInstance(workspaces);
    }

    @ApiOperation(value = "", notes = "获取所有的云平台工作空间")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/query-kubesphere-workspace")
    @LogNoneRecord
    public DefaultRspDTO<List<KubesphereMetaData>> queryKubesphereWorkspace(@Validated @RequestBody EmergencyContainerAddReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        List<KubesphereMetaData> workspaces = kubesphereClient.queryWorkspaces(BeanConvertUtil.convert(reqDTO, EmergencyContainerBO.class));
        return DefaultRspDTO.newSuccessInstance(workspaces);
    }

    @ApiOperation(value = "", notes = "查询流程实例列表")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/query-pods")
    @LogNoneRecord
    public DefaultRspDTO<Map<String, List<String>>> queryPods(@Validated @RequestBody QueryPodsReqDto reqDto) {
        String workspaceId = TenantUtils.getWorkspaceIdNotNull();
        Map<String, List<String>> pods = kubesphereClient.queryPods(workspaceId, reqDto.getCluster(), reqDto.getNamespace());
        if (JudgeUtils.isEmpty(pods)) {
            return DefaultRspDTO.newSuccessInstance(new HashMap<>());
        }
        return DefaultRspDTO.newSuccessInstance(pods);
    }

    @ApiOperation(value = "", notes = "查询pos节点")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/query-node-pods")
    @LogNoneRecord
    public DefaultRspDTO<List<String>> queryNodePods(@Validated @RequestBody QueryPodsReqDto reqDto) {
        String workspaceId = TenantUtils.getWorkspaceIdNotNull();
        Map<String, List<String>> pods = kubesphereClient.queryPods(workspaceId, reqDto.getCluster(), reqDto.getNamespace());
        return DefaultRspDTO.newSuccessInstance(pods.getOrDefault(reqDto.getAppName(),new ArrayList<>()));
    }
}
