package com.cmpay.hacp.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.plugin.IndicatorMetadataService;
import com.cmpay.hacp.inspection.application.service.InspectionPluginService;
import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.hacp.inspection.assembler.IndicatorDTOMapper;
import com.cmpay.hacp.inspection.assembler.IndicatorParamDTOMapper;
import com.cmpay.hacp.inspection.assembler.InspectionPluginDTOMapper;
import com.cmpay.hacp.inspection.assembler.TagDTOMapper;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;
import com.cmpay.hacp.inspection.dto.*;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 巡检插件管理
 */
@RestController
@RequestMapping(VersionApi.VERSION_V1 + "/inspection/plugin")
@Tag(name = "巡检插件管理")
@RequiredArgsConstructor
public class InspectionPluginController {

    private final InspectionPluginService inspectionPluginService;
    private final InspectionPluginDTOMapper inspectionPluginDTOMapper;
    private final TagService tagService;
    private final TagDTOMapper tagDTOMapper;
    private final IndicatorMetadataService indicatorMetadataService;
    private final IndicatorParamDTOMapper indicatorParamDTOMapper;
    private final IndicatorDTOMapper indicatorDTOMapper;

    /**
     * 创建巡检插件
     *
     * @param reqDTO 请求参数
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建巡检插件", description = "创建新的巡检插件")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:create')")
    public DefaultRspDTO<String> createPlugin(@Validated @RequestBody InspectionPluginReqDTO reqDTO) {
        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        String pluginId = inspectionPluginService.createPlugin(inspectionPlugin);

        return DefaultRspDTO.newSuccessInstance(pluginId);
    }

    /**
     * 更新巡检插件
     *
     * @param reqDTO 请求参数
     * @return 更新结果
     */
    @PutMapping("/update")
    @Operation(summary = "更新巡检插件", description = "更新现有的巡检插件")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:update')")
    public DefaultRspDTO<NoBody> updatePlugin(@Validated @RequestBody InspectionPluginReqDTO reqDTO) {
        // 验证插件ID
        if (reqDTO.getPluginId() == null) {
            return DefaultRspDTO.newInstance(ErrorCodeEnum.PLUGIN_ID_REQUIRED);
        }

        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        // 调用服务更新插件
        boolean success = inspectionPluginService.updatePlugin(inspectionPlugin);

        if (success) {
            return DefaultRspDTO.newSuccessInstance();
        } else {
            return DefaultRspDTO.newInstance(ErrorCodeEnum.PLUGIN_UPDATE_FAILED);
        }
    }

    /**
     * 删除巡检插件
     *
     * @param pluginId 插件ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{pluginId}")
    @Operation(summary = "删除巡检插件", description = "删除指定的巡检插件")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:delete')")
    public DefaultRspDTO<NoBody> deletePlugin(
            @Parameter(name = "pluginId", description = "插件ID", required = true)
            @PathVariable("pluginId") String pluginId) {
        inspectionPluginService.deletePlugin(pluginId);

        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 获取巡检插件详情
     *
     * @param pluginId 插件ID
     * @return 插件详情
     */
    @GetMapping("/detail/{pluginId}")
    @Operation(summary = "获取巡检插件详情", description = "获取指定巡检插件的详细信息")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<InspectionPluginRspDTO> getPluginDetail(
            @Parameter(name = "pluginId", description = "插件ID", required = true)
            @PathVariable("pluginId") String pluginId) {

        // 获取插件详情
        InspectionPlugin inspectionPlugin = inspectionPluginService.getPluginDetail(pluginId);

        List<Long> tagIds = inspectionPlugin.getTagIds();
        List<com.cmpay.hacp.inspection.domain.model.common.Tag> tags = tagService.getTagByTagIds(tagIds);
        InspectionPluginRspDTO rspDTO = inspectionPluginDTOMapper.toInspectionPluginRspDTO(inspectionPlugin);
        rspDTO.setTags(tagDTOMapper.toTagDTOList(tags));

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }

    /**
     * 分页查询巡检插件列表
     *
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询巡检插件列表", description = "根据条件分页查询巡检插件列表")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<PageDTO<InspectionPluginRspDTO>> getPluginPage(@Validated @RequestBody InspectionPluginQueryReqDTO reqDTO) {
        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        IPage<InspectionPlugin> page = inspectionPluginService.getPluginPage(reqDTO.getPage(), inspectionPlugin);
        List<InspectionPluginRspDTO> rspDTOList = inspectionPluginDTOMapper.toInspectionPluginRspDTOList(page.getRecords());
        PageDTO<InspectionPluginRspDTO> result = new PageDTO<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(rspDTOList);

        return DefaultRspDTO.newSuccessInstance(result);
    }

    @GetMapping("/indicator/{indicatorId}")
    @Operation(summary = "获取指标定义", description = "获取指定指标的定义信息")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<List<IndicatorParamDTO>> getIndicatorDefinition(
            @Parameter(name = "indicatorId", description = "指标ID", required = true)
            @PathVariable("indicatorId") String indicatorId) {
        IndicatorDefinition indicatorDefinition = indicatorMetadataService.getIndicatorParam(indicatorId);
        return DefaultRspDTO.newSuccessInstance(indicatorParamDTOMapper.toIndicatorParamDTO(indicatorDefinition));
    }

    @GetMapping("/indicator/type/{indicatorType}")
    @Operation(summary = "获取指标定义列表", description = "获取指定指标类型的定义列表")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<List<IndicatorDTO>> getIndicatorDefinitionsByType(
            @Parameter(name = "indicatorType", description = "指标类型", required = true)
            @PathVariable("indicatorType") IndicatorType indicatorType) {
        List<IndicatorDefinition> indicatorDefinitions = indicatorMetadataService.getIndicatorDefinitionsByType(indicatorType);
        return DefaultRspDTO.newSuccessInstance(indicatorDTOMapper.toIndicatorDTOList(indicatorDefinitions));
    }
}
