package com.cmpay.hacp.inspection.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 按次巡检报告详细内容响应DTO
 */
@Data
@Schema(description = "按次巡检报告详细内容响应DTO")
public class PerInspectionReportDetailRspDTO {

    @Schema(description = "报告ID", example = "RPT-202503240003")
    private String reportId;

    @Schema(description = "执行概况")
    private ExecutionSummaryDTO executionSummary;

    @Schema(description = "执行结果分布（饼图数据）")
    private ExecutionDistributionDTO executionDistribution;

    @Schema(description = "执行时间分布（柱状图数据）")
    private ExecutionTimeDistributionDTO timeDistribution;

    @Schema(description = "规则检查详情列表")
    private List<RuleCheckDetailDTO> ruleCheckDetails;

    @Schema(description = "异常详情列表（问题汇总）")
    private List<ExceptionDetailDTO> exceptionDetails;

    /**
     * 执行概况DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "执行概况")
    public static class ExecutionSummaryDTO {
        @Schema(description = "开始时间", example = "2025-03-24T10:00:00")
        private LocalDateTime startTime;

        @Schema(description = "结束时间", example = "2025-03-24T10:05:30")
        private LocalDateTime endTime;

        @Schema(description = "执行耗时（秒）", example = "330")
        private Long duration;

        @Schema(description = "执行耗时字符串", example = "5分30秒")
        private String durationStr;

        @Schema(description = "规则总数", example = "10")
        private Integer totalRules;

        @Schema(description = "成功规则数", example = "8")
        private Integer successRules;
    }

    /**
     * 执行结果分布DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "执行结果分布")
    public static class ExecutionDistributionDTO {
        @Schema(description = "总检查数", example = "10")
        private Integer total;

        @Schema(description = "通过率百分比", example = "80")
        private Integer successRate;

        @Schema(description = "各状态数量")
        private List<StatusCountDTO> statusCounts;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @Schema(description = "状态数量")
        public static class StatusCountDTO {
            @Schema(description = "状态名称", example = "通过")
            private String status;

            @Schema(description = "状态编码", example = "passed")
            private String statusCode;

            @Schema(description = "数量", example = "8")
            private Integer count;
        }
    }

    /**
     * 执行时间分布DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "执行时间分布")
    public static class ExecutionTimeDistributionDTO {
        @Schema(description = "时间区间数据")
        private List<TimeRangeDTO> timeRanges;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @Schema(description = "时间区间")
        public static class TimeRangeDTO {
            @Schema(description = "时间区间标签", example = "<1秒")
            private String label;

            @Schema(description = "该区间的数量", example = "3")
            private Integer count;
        }
    }

    /**
     * 规则检查详情DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "规则检查详情")
    public static class RuleCheckDetailDTO {
        @Schema(description = "规则ID", example = "RULE-001")
        private String ruleId;

        @Schema(description = "规则名称", example = "CPU使用率检查")
        private String ruleName;

        @Schema(description = "资源名称", example = "服务器-001")
        private String resourceName;

        @Schema(description = "执行状态", example = "SUCCESS")
        private String executionStatus;

        @Schema(description = "执行结果", example = "CPU使用率: 45%")
        private String executionResult;

        @Schema(description = "执行时间", example = "2025-03-24T10:01:00")
        private LocalDateTime executionTime;

        @Schema(description = "执行耗时（毫秒）", example = "1500")
        private Long executionDuration;
    }

    /**
     * 异常详情DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "异常详情")
    public static class ExceptionDetailDTO {
        @Schema(description = "规则ID", example = "RULE-002")
        private String ruleId;

        @Schema(description = "规则名称", example = "内存使用率检查")
        private String ruleName;

        @Schema(description = "异常描述", example = "内存使用率超过阈值")
        private String description;

        @Schema(description = "资源名称", example = "服务器-002")
        private String resourceName;

        @Schema(description = "建议措施", example = "建议清理内存或增加内存容量")
        private String suggestion;
    }
}
