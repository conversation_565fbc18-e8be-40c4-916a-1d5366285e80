package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.IntervalUnit;
import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 固定间隔调度配置DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("INTERVAL")
@Schema(description = "固定间隔调度配置DTO")
public class IntervalScheduleConfigDTO extends ScheduleConfigDTO {

    @Schema(description = "间隔值", required = true, example = "30")
    @NotNull(message = "间隔值不能为空")
    @Min(value = 1, message = "间隔值必须大于0")
    private Integer intervalValue;

    @Schema(description = "间隔单位", required = true, example = "MINUTE")
    @NotNull(message = "间隔单位不能为空")
    private IntervalUnit intervalUnit;

    public IntervalScheduleConfigDTO() {
        setType(ScheduleType.FIXED_INTERVAL);
    }
}
