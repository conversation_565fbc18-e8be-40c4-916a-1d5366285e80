package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ParamType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 插件脚本参数设置DTO
 */
@Data
@Schema(description = "插件脚本参数设置DTO")
public class PluginScriptParameterDTO {
    /**
     * 参数名称
     */
    @Schema(description = "参数名称", required = true, example = "threshold")
    @NotBlank(message = "参数名称不能为空")
    private String paramName;

    @Schema(description = "参数类型：1-文本, 2-数字, 3-邮箱, 4-URL, 5-正则表达式", required = true, example = "1")
    @NotNull(message = "参数类型不能为空")
    private ParamType paramType;

    /**
     * 正则表达式
     */
    @Schema(description = "正则表达式", required = true, example = "^[0-9a-zA-Z.]+$")
    @NotBlank(message = "正则表达式不能为空")
    private String regexPattern;

    /**
     * 参数值示例
     */
    @Schema(description = "参数值示例", required = true, example = "90")
    @NotBlank(message = "参数值示例不能为空")
    private String paramValue;

    /**
     * 参数描述
     */
    @Schema(description = "参数描述", required = true, example = "告警阈值百分比")
    @NotBlank(message = "参数描述不能为空")
    private String paramDesc;

    /**
     * 是否加密(0不加密，1加密)
     */
    @Schema(description = "是否加密：true-加密，false-不加密", example = "false")
    private Boolean isEncrypted;
}
