package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "巡检规则查询请求DTO")
public class InspectionRuleQueryReqDTO {

    @Schema(description = "规则ID", example = "1001")
    private String ruleId;

    @Schema(description = "规则名称", example = "CPU 使用率阈值检查")
    private String name;

    @Schema(description = "规则描述", example = "当 CPU 使用率超过阈值时触发告警")
    private String description;

    private PageDTO<?> page;

}
