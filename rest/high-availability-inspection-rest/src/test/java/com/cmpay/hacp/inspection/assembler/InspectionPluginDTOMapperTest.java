package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.enums.*;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.model.plugin.PluginScriptParameter;
import com.cmpay.hacp.inspection.domain.model.plugin.PluginScriptResult;
import com.cmpay.hacp.inspection.dto.InspectionPluginReqDTO;
import com.cmpay.hacp.inspection.dto.PluginScriptParameterDTO;
import com.cmpay.hacp.inspection.dto.PluginScriptResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 巡检插件对象转换器单元测试
 */
public class InspectionPluginDTOMapperTest {

    private InspectionPluginDTOMapper mapper;
    private InspectionPluginReqDTO reqDTO;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(InspectionPluginDTOMapper.class);

        // 创建测试数据
        reqDTO = new InspectionPluginReqDTO();
        reqDTO.setPluginId("1001");
        reqDTO.setName("CPU使用率检查");
        reqDTO.setType(PluginType.SHELL_SCRIPT);
        reqDTO.setStatus(PluginStatus.ENABLE);
        reqDTO.setDescription("检查服务器CPU使用率是否超过阈值");
        reqDTO.setScriptContent("#!/bin/bash\necho \"CPU使用率检查\"");
        reqDTO.setScriptResultType(ScriptResultType.STRUCTURED); // 结构化
        reqDTO.setTagIds(Arrays.asList(1L, 2L, 3L));

        // 设置输出字段定义
        List<PluginScriptResultDTO> results = new ArrayList<>();
        PluginScriptResultDTO result = new PluginScriptResultDTO();
        result.setFieldName("cpu.usage");
        result.setExampleValue("85.5");
        result.setFieldUnit("%");
        result.setFieldType(ScriptResultFieldType.NUMERIC);
        result.setDescription("CPU使用率");
        results.add(result);
        reqDTO.setResults(results);

        // 设置参数设置
        List<PluginScriptParameterDTO> parameters = new ArrayList<>();
        PluginScriptParameterDTO parameter = new PluginScriptParameterDTO();
        parameter.setParamName("threshold");
        parameter.setParamType(ParamType.TEXT);
        parameter.setRegexPattern("^[0-9]+$");
        parameter.setParamValue("90");
        parameter.setParamDesc("告警阈值百分比");
        parameter.setIsEncrypted(false);
        parameters.add(parameter);
        reqDTO.setParameters(parameters);
    }

    @Test
    @DisplayName("测试请求DTO转领域对象")
    void testToInspectionPlugin() {
        // 执行转换
        InspectionPlugin result = mapper.toInspectionPlugin(reqDTO);

        // 验证基本字段映射
        assertNotNull(result);
        assertEquals(reqDTO.getPluginId(), result.getPluginId());
        assertEquals(reqDTO.getName(), result.getName());
        assertEquals(reqDTO.getDescription(), result.getDescription());
        assertEquals(reqDTO.getScriptContent(), result.getScriptContent());

        // 验证枚举类型转换
        assertEquals(PluginType.SHELL_SCRIPT, result.getType());
        assertEquals(PluginStatus.ENABLE, result.getStatus());
        assertEquals(ScriptResultType.STRUCTURED, result.getScriptResultType());

        // 验证集合映射 - 标签ID
        assertNotNull(result.getTagIds());
        assertEquals(reqDTO.getTagIds().size(), result.getTagIds().size());
        assertTrue(result.getTagIds().containsAll(reqDTO.getTagIds()));

        // 验证输出字段定义映射
        assertNotNull(result.getResults());
        assertEquals(1, result.getResults().size());
        PluginScriptResult outputResult = result.getResults().get(0);
        assertEquals("cpu.usage", outputResult.getFieldName());
        assertEquals("85.5", outputResult.getExampleValue());
        assertEquals("%", outputResult.getFieldUnit());
        assertEquals(ScriptResultFieldType.NUMERIC, outputResult.getFieldType());
        assertEquals("CPU使用率", outputResult.getDescription());

        // 验证参数设置映射
        assertNotNull(result.getParameters());
        assertEquals(1, result.getParameters().size());
        PluginScriptParameter paramResult = result.getParameters().get(0);
        assertEquals("threshold", paramResult.getParamName());
        assertEquals("^[0-9]+$", paramResult.getRegexPattern());
        assertEquals("90", paramResult.getParamValue());
        assertEquals("告警阈值百分比", paramResult.getParamDesc());
        assertEquals(false, paramResult.getIsEncrypted());
    }
}
