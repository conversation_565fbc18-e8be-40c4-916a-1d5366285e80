package com.cmpay.hacp.system.controller;


import com.cmpay.hacp.system.service.SystemCaptchaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 */
@Api(tags = "验证码管理")
@ConditionalOnMissingBean(SystemCaptchaDevController.class)
@RestController
public class SystemCaptchaController {

    @Autowired
    private SystemCaptchaService systemCaptchaService;

    /**
     * 生成图片验证码
     *
     * @param request
     * @return
     */
    @ApiOperation("获取图片验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "随机数",
                    name = "captchaReqId",
                    dataType = "java.lang.String",
                    paramType = "query",
                    example = "123456",
                    required = true)
    })
    @GetMapping(value = {"/upms/v1/captcha/getCaptcha", "/v1/sys/captcha/getCaptcha"},
            produces = {MediaType.IMAGE_JPEG_VALUE, MediaType.IMAGE_GIF_VALUE, MediaType.IMAGE_PNG_VALUE})
    public void captcha(HttpServletRequest request, HttpServletResponse resp, String captchaReqId) {
        systemCaptchaService.captcha(request, resp, captchaReqId);
    }


}
