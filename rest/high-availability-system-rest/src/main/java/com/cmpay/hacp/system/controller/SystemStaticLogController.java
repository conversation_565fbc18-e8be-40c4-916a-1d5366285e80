package com.cmpay.hacp.system.controller;

import com.cmpay.hacp.api.BaseApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.system.bo.system.SystemLogBO;
import com.cmpay.hacp.dto.system.SystemLogDTO;
import com.cmpay.hacp.dto.system.SystemLogQueryDTO;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.system.log.bo.SysStaticLogBO;
import com.cmpay.hacp.system.log.service.SystemStaticLogService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.annotation.QueryBody;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(VersionApi.VERSION_V1 + BaseApi.BASE_SYSTEM)
@Api(tags = "系统日志管理")
@Validated
public class SystemStaticLogController {

    @Resource
    private SystemStaticLogService systemStaticLogService;

    /**
     * 查询系统日志列表
     *
     * @param systemLogQueryDto 日志详情
     * @return 系统日志列表
     */
    @GetMapping("/sys/logs")
    @ApiOperation(value = "查询系统日志列表", notes = "查询系统日志列表")
    @ApiResponse(code = 200, message = "系统日志列表")
    @LogNoneRecord
    public DefaultRspDTO<PageInfo<SystemLogDTO>> getSystemLogs(@Validated @QueryBody SystemLogQueryDTO systemLogQueryDto) {
        SystemLogBO systemLog = new SystemLogBO();
        BeanUtils.copyProperties(systemLog, systemLogQueryDto);
        PageInfo systemLogPageInfo = systemStaticLogService.list(systemLogQueryDto.getPageNum(),
                systemLogQueryDto.getPageSize(),
                systemLog);
        PageInfo pageInfo = new PageInfo<>(new ArrayList<>());
        BeanUtils.copyProperties(pageInfo, systemLogPageInfo);
        if (JudgeUtils.isNotEmpty(systemLogPageInfo.getList())) {
            pageInfo.setList(BeanConvertUtil.convertList(systemLogPageInfo.getList(), SystemLogDTO.class));
        }
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    /**
     * 查询我的操作日志列表
     *
     * @param systemLogQueryDto 日志详情
     * @return 系统日志列表
     */
    @GetMapping("/sys/own/logs")
    @ApiOperation(value = "查询我的操作日志列表", notes = "查询我的操作日志列表")
    @ApiResponse(code = 200, message = "我的操作日志列表")
    @LogRecord(title = "查询个人操作日志", action = "查询")
    public DefaultRspDTO<PageInfo<SystemLogDTO>> getOwnSystemLogs(@Validated @QueryBody SystemLogQueryDTO systemLogQueryDto) {
        SystemLogBO systemLog = new SystemLogBO();
        BeanUtils.copyProperties(systemLog, systemLogQueryDto);
        systemLog.setUserId(SecurityUtils.getLoginUserId());
        PageInfo systemLogPageInfo = systemStaticLogService.list(systemLogQueryDto.getPageNum(),
                systemLogQueryDto.getPageSize(),
                systemLog);
        PageInfo pageInfo = new PageInfo<>(new ArrayList<>());
        BeanUtils.copyProperties(pageInfo, systemLogPageInfo);
        if (JudgeUtils.isNotEmpty(systemLogPageInfo.getList())) {
            pageInfo.setList(BeanConvertUtil.convertList(systemLogPageInfo.getList(), SystemLogDTO.class));
        }
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    /**
     * 查询操作日志详情
     *
     * @param id 操作日志ID
     * @return 操作日志详情
     */
    @GetMapping("/sys/log/info")
    @ApiOperation(value = "查询操作日志详情", notes = "查询操作日志详情")
    @ApiResponse(code = 200, message = "操作日志详情")
    @LogNoneRecord
    public DefaultRspDTO<SystemLogDTO> getSystemLogInfo(@ApiParam(name = "id",
            value = "操作日志ID",
            required = true,
            example = "0179035dc433428c84ff434379374157")
    @NotBlank(message = "SMD00009") @RequestParam("id") String id) {
        SysStaticLogBO sysStaticLogBO = systemStaticLogService.info(id);
        SystemLogDTO systemLogDTO = new SystemLogDTO();
        if (JudgeUtils.isNotNull(sysStaticLogBO)) {
            BeanUtils.copyProperties(systemLogDTO, sysStaticLogBO);
        }
        return DefaultRspDTO.newSuccessInstance(systemLogDTO);
    }

    /**
     * 登记操作日志
     *
     * @param systemLogDto 操作日志详情
     * @return
     */
    @PostMapping("/sys/log/add")
    @ApiOperation(value = "登记操作日志", notes = "登记操作日志")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    public DefaultRspDTO<NoBody> addSystemLogInfo(@RequestBody SystemLogDTO systemLogDto) {
        SystemLogBO systemLog = new SystemLogBO();
        BeanUtils.copyProperties(systemLog, systemLogDto);
        systemStaticLogService.add(systemLog);
        return DefaultRspDTO.newSuccessInstance();
    }

}
