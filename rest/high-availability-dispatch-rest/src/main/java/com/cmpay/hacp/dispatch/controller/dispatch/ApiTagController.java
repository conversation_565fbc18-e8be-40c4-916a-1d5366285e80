package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dispatch.controller.dto.ApiLocationApiTagReqDTO;
import com.cmpay.hacp.dispatch.controller.dto.ApiTagReqDTO;
import com.cmpay.hacp.dispatch.bo.ApiLocationApiTagBO;
import com.cmpay.hacp.dispatch.bo.ApiTagBO;
import com.cmpay.hacp.dispatch.service.ApiLocationApiTagService;
import com.cmpay.hacp.dispatch.service.ApiTagService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/api-tag")
@Api(tags = "接口标签")
public class ApiTagController {

    @Autowired
    private ApiTagService apiTagService;
    @ApiOperation(value = "单纯添加接口标签", notes = "单纯添加接口标签", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/add")
    @LogRecord(title = "新增接口标签", action = "新增")
    @PreAuthorize("hasPermission('ApiTagController','dispatch:api-tag:add')")
    public DefaultRspDTO<ApiTagBO> addApiTag(@RequestBody ApiTagReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        ApiTagBO apiTagBO = apiTagService.addApiTag(reqDTO);
        return DefaultRspDTO.newSuccessInstance(apiTagBO);
    }

    @ApiOperation(value = "获取全量接口标签列表", notes = "获取全量接口标签列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/list-all-tag")
    @LogNoneRecord
    public DefaultRspDTO<List<ApiTagBO>> getApiTagList(ApiTagReqDTO reqDTO) {
        Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(reqDTO::setWorkspaceId);
        List<ApiTagBO> apiTagList = apiTagService.getApiTagList(reqDTO);
        return DefaultRspDTO.newSuccessInstance(apiTagList);
    }

    @Autowired
    private ApiLocationApiTagService apiLocationApiTagService;
    @ApiOperation(value = "给接口添加tag", notes = "给接口添加tag", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "新增接口标签", action = "新增")
    @PostMapping("/add-api-tag-for-api")
    public DefaultRspDTO<NoBody> addApiTagForApiLocation(@RequestBody ApiLocationApiTagReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        apiLocationApiTagService.addApiLocationApiTag(reqDTO);
        return DefaultRspDTO.newInstance(MsgEnum.SUCCESS);
    }

    @ApiOperation(value = "获取接口绑定的tag", notes = "获取接口绑定的tag", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/list-api-tag")
    @LogNoneRecord
    public GenericRspDTO<List<ApiLocationApiTagBO>> getApiLocationApiTagList(ApiLocationApiTagReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        List<ApiLocationApiTagBO> apiLocationApiTagBOList = apiLocationApiTagService.getApiLocationApiTagList(reqDTO);
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, apiLocationApiTagBOList);
    }

    @ApiOperation(value = "删除接口绑定的tag", notes = "删除接口绑定的tag", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/delete/{id}")
    @LogRecord(title = "删除接口标签", action = "删除")
    @PreAuthorize("hasPermission('ApiTagController','dispatch:api-tag:delete')")
    public DefaultRspDTO<NoBody> deleteByTagId(@PathVariable("id") Integer id){
        ApiTagBO apiTagBO = new ApiTagBO();
        TenantSecurityUtils.copyTenantSecurity(apiTagBO);
        apiTagBO.setApiTagId(id);
        apiTagService.deleteApiTag(apiTagBO);
        return DefaultRspDTO.newSuccessInstance();
    }
}