package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dispatch.controller.dto.DispatchRulePageableReqDTO;
import com.cmpay.hacp.dispatch.controller.dto.DispatchRuleReqDTO;
import com.cmpay.hacp.dispatch.bo.DispatchRuleBO;
import com.cmpay.hacp.dispatch.service.DispatchRuleService;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hafr.agent.dto.DictionaryReport;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Api(tags = "规则管理")
@RestController
@RequestMapping("/v1/dispatch-rule")
public class DispatchRuleController {

    @Autowired
    private DispatchRuleService dispatchRuleService;
    @ApiOperation(value = "add", notes = "添加调度规则-全量数据", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/add")
    @LogRecord(title = "添加调度规则", action = "新增")
    @PreAuthorize("hasPermission('DispatchRuleController','dispatch:dispatch-rule:add')")
    public DefaultRspDTO<NoBody> add(@RequestBody DispatchRuleReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        dispatchRuleService.addDispatchRule(BeanConvertUtil.convert(reqDTO, DispatchRuleBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "page", notes = "获取调度规则分页列表-模糊搜索", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/page")
    @PreAuthorize("hasPermission('DispatchRuleController','dispatch:dispatch-rule:query')")
    public DefaultRspDTO<PageInfo<DispatchRuleBO>> getPage(DispatchRulePageableReqDTO reqDTO) {
        Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(workspaceId -> reqDTO.setWorkspaceId(workspaceId));
        PageInfo<DispatchRuleBO> pageInfo = dispatchRuleService.getDispatchRulePage(reqDTO.getPageNum(), reqDTO.getPageSize(), BeanConvertUtil.convert(reqDTO, DispatchRuleBO.class));
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "list", notes = "获取调度规则列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/list")
    public DefaultRspDTO<List<DispatchRuleBO>> getList() {
        List<DispatchRuleBO> list = dispatchRuleService.getDispatchRuleList(TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "delete", notes = "删除调度规则", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/delete")
    @LogRecord(title = "删除调度规则", action = "删除")
    @PreAuthorize("hasPermission('DispatchRuleController','dispatch:dispatch-rule:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody DispatchRuleReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        dispatchRuleService.deleteDispatchRule(reqDTO.getDispatchRuleId());
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "update", notes = "更新调度规则-全量数据", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "修改调度规则", action = "修改")
    @PostMapping("/update")
    @PreAuthorize("hasPermission('DispatchRuleController','dispatch:dispatch-rule:update')")
    public DefaultRspDTO<NoBody> update(@RequestBody DispatchRuleReqDTO reqDTO) {
        TenantSecurityUtils.copyOperator(reqDTO);
        dispatchRuleService.updateDispatchRule(BeanConvertUtil.convert(reqDTO, DispatchRuleBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }
    @ApiOperation(value = "orderGetRules", notes = "获取调度规则详情-用于规则详情-支持批量用于生成调度预览", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "查询调度规则详情", action = "查询")
    @PostMapping("/info")
    @PreAuthorize("hasPermission('DispatchRuleController','dispatch:dispatch-rule:info')")
    public GenericRspDTO<List<DispatchRuleBO>> getInfo(@RequestBody List<Integer> dispatchRuleIds) {
        List<DispatchRuleBO> list = new ArrayList<>();
        if(dispatchRuleIds != null && !dispatchRuleIds.isEmpty()) {
            for (Integer dispatchRuleId : dispatchRuleIds) {
                DispatchRuleBO dispatchRule = dispatchRuleService.getDispatchRule(dispatchRuleId);
                if(dispatchRule != null) {
                    list.add(dispatchRule);
                }
            }
        }
        return GenericRspDTO.newSuccessInstance(list);
    }


    @ApiOperation(value = "orderGetRules", notes = "获取调度规则详情-用于调度详情查询-支持批量用于生成调度预览", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @PostMapping("/orderGetRules")
    public GenericRspDTO<List<DispatchRuleBO>> orderGetRules(@RequestBody List<Integer> dispatchRuleIds) {
        List<DispatchRuleBO> list = new ArrayList<>();
        if(dispatchRuleIds != null && !dispatchRuleIds.isEmpty()) {
            for (Integer dispatchRuleId : dispatchRuleIds) {
                DispatchRuleBO dispatchRule = dispatchRuleService.getDispatchRule(dispatchRuleId);
                if(dispatchRule != null) {
                    list.add(dispatchRule);
                }
            }
        }
        return GenericRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "getRuleDictionary", notes = "获取规则字典", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/getRuleDictionary")
    public DefaultRspDTO<DictionaryReport> getRuleDictionary() {
        DictionaryReport ruleDictionary = dispatchRuleService.getRuleDictionary(TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance(ruleDictionary);
    }
}