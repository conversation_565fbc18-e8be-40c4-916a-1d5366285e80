package com.cmpay.hacp.dispatch.cron;

import com.cmpay.hacp.dispatch.service.DispatchPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.Closeable;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "hacp.push.watch", name = "enable", havingValue = "true")
public class DispatchConfigPushWatch implements Closeable {

    private Lock fetchLock = new ReentrantLock();

    @Autowired
    private DispatchPushService dispatchPushService;

    @Scheduled(initialDelayString = "${hacp.push.watch.initialDelay:30000}",
            fixedDelayString = "${hacp.push.watch.delay:300000}")
    public void pushConfigToAgent() {
        dispatchPushService.pushConfigToNodeByWorkspace();
        log.info("Push current config to all agent.");
    }

    @Override
    public void close() {

    }

}
