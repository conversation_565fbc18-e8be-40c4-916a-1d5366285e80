/*
 * @ClassName IEmergencyHostArchiveDao
 * @Description 
 * @version 1.0
 * @Date 2024-10-30 16:34:58
 */
package com.cmpay.hacp.emergency.dao;

import com.cmpay.hacp.emergency.bo.EmergencyHostArchiveBO;
import com.cmpay.hacp.emergency.entity.EmergencyHostArchiveDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IEmergencyHostArchiveExtDao extends IEmergencyHostArchiveDao {
    int batchInsert(@Param("list") List<EmergencyHostArchiveBO> hostArchiveBO,@Param("businessKey") String businessKey, @Param("workspaceId") String workspaceId, @Param("activityId") String activityId);

    List<EmergencyHostArchiveDO> findExt(EmergencyHostArchiveDO entity);
}