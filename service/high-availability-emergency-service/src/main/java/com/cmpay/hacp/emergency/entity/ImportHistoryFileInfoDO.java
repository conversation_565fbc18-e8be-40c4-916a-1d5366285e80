/*
 * @ClassName ImportHistoryFileInfoDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-30 14:19:16
 */
package com.cmpay.hacp.emergency.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class ImportHistoryFileInfoDO extends BaseDO {
    /**
     * @Fields id 主键
     */
    private Integer id;
    /**
     * @Fields fileName 文件名称
     */
    private String fileName;
    /**
     * @Fields excelCount API导入信息总计
     */
    private Integer excelCount;
    /**
     * @Fields excelSuccess API导入信息成功数
     */
    private Integer excelSuccess;
    /**
     * @Fields excelFail API导入信息失败数
     */
    private Integer excelFail;
    /**
     * @Fields excelSuccessRemark 成功序列（1,2,3）
     */
    private String excelSuccessRemark;
    /**
     * @Fields excelFailRemark 失败序列（4,5,6）
     */
    private String excelFailRemark;
    /**
     * @Fields batchNumber 批次号
     */
    private String batchNumber;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields workspaceId 项目编号
     */
    private String workspaceId;
    /**
     * @Fields operator 操作人
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作名
     */
    private String operatorName;
    /**
     * @Fields fileBlob 文件内容
     */
    private byte[] fileBlob;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getExcelCount() {
        return excelCount;
    }

    public void setExcelCount(Integer excelCount) {
        this.excelCount = excelCount;
    }

    public Integer getExcelSuccess() {
        return excelSuccess;
    }

    public void setExcelSuccess(Integer excelSuccess) {
        this.excelSuccess = excelSuccess;
    }

    public Integer getExcelFail() {
        return excelFail;
    }

    public void setExcelFail(Integer excelFail) {
        this.excelFail = excelFail;
    }

    public String getExcelSuccessRemark() {
        return excelSuccessRemark;
    }

    public void setExcelSuccessRemark(String excelSuccessRemark) {
        this.excelSuccessRemark = excelSuccessRemark;
    }

    public String getExcelFailRemark() {
        return excelFailRemark;
    }

    public void setExcelFailRemark(String excelFailRemark) {
        this.excelFailRemark = excelFailRemark;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public byte[] getFileBlob() {
        return fileBlob;
    }

    public void setFileBlob(byte[] fileBlob) {
        this.fileBlob = fileBlob;
    }
}