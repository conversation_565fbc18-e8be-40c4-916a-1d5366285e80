package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.DispatchZoneBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/05/15 10:20
 * @since 1.0.0
 */

public interface DispatchZoneService {


    /**
     * 新增
     *
     * @param entity
     */
    void add( DispatchZoneBO entity);

    /**
     * 修改
     *
     * @param entity      信息
     */
    void update(DispatchZoneBO entity);

    /**
     * 删除
     *
     * @param id            同上
     * @param workspaceId   工作区 ID
     */
    void delete(Long id, String workspaceId);

    DispatchZoneBO getInfo(String id, String workspaceId);

    /**
     * 获取页面信息
     *
     * @param pageNum      页码
     * @param pageSize     页面大小
     * @param dispatchZoneBO 租户房 BO
     * @return {@link PageInfo}<{@link DispatchZoneBO}>
     */
    PageInfo<DispatchZoneBO> getPageInfo(int pageNum, int pageSize, DispatchZoneBO dispatchZoneBO);

    /**
     * 获取简单列表
     *
     * @param workspaceId 工作区 ID
     * @return {@link List}<{@link DispatchZoneBO}>
     */
    List<DispatchZoneBO> getSimpleList(String workspaceId);

    /**
     * 获取简单map
     *
     * @param workspaceId 工作区 ID
     * @return {@link List}<{@link DispatchZoneBO}>
     */
    Map<Long,String> getSimpleMap(String workspaceId);
}
