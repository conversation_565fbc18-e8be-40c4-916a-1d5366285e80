package com.cmpay.hacp.dispatch.util;

import com.cmpay.hacp.dispatch.bo.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Data
@Slf4j
public class JsonSerializationUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
        objectMapper.registerModule(javaTimeModule);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static ObjectMapper getObjectMapper(){
        return objectMapper;
    }
    public static String toString(Object list) {
        if (list != null) {
            try {
                return objectMapper.writeValueAsString(list);
            } catch (Exception e) {
                log.info("name:{}, message", e.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

    public static List<AppServiceBO> toAppServiceBOList(String string) {
        if(StringUtils.isNotBlank(string)) {
            try {
                return objectMapper.readValue(string, new TypeReference<List<AppServiceBO>>() {});
            } catch (JsonProcessingException e) {
                log.info("name:{}, message", e.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

    public static List<AppInstanceGroupBO> toAppInstanceGroupBOList(String string) {
        if(StringUtils.isNotBlank(string)) {
            try {
                return objectMapper.readValue(string, new TypeReference<List<AppInstanceGroupBO>>() {});
            } catch (JsonProcessingException e) {
                log.info("name:{}, message", e.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

    public static List<ApiLocationBO> toApiLocationBOList(String string) {
        if(StringUtils.isNotBlank(string)) {
            try {
                return objectMapper.readValue(string, new TypeReference<List<ApiLocationBO>>() {});
            } catch (JsonProcessingException e) {
                log.info("name:{}, message", e.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

    public static List<DispatchBO> toDispatchBOList(String string) {
        if(StringUtils.isNotBlank(string)) {
            try {
                return objectMapper.readValue(string, new TypeReference<List<DispatchBO>>() {});
            } catch (JsonProcessingException e) {
                log.info("name:{}, message", e.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

    public static List<DispatchRuleBO> toDispatchRuleBOList(String string) {
        if(StringUtils.isNotBlank(string)) {
            try {
                return objectMapper.readValue(string, new TypeReference<List<DispatchRuleBO>>() {});
            } catch (JsonProcessingException e) {
                log.info("name:{}, message", e.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

    public static Map<String, Set<String>> toMap(String string) {
        if(StringUtils.isNotBlank(string)) {
            try {
                return objectMapper.readValue(string, new TypeReference<Map<String, Set<String>> >() {});
            } catch (JsonProcessingException e) {
                log.info("name:{}, message", e.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

    public static Map<String, String> toStringMap(String string) {
        if(StringUtils.isNotBlank(string)) {
            try {
                return objectMapper.readValue(string, new TypeReference<Map<String, String>>() {});
            } catch (JsonProcessingException e) {
                log.info("name:{}, message", e.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

}
