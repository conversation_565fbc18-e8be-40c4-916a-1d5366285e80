package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;


@Data
@EqualsAndHashCode(callSuper=true)
public class DispatchPushHistoryBO extends DispatchPushHistoryDO implements TenantCapable {

    private String workspaceId;

    public String getNodeCallUrl(){
        if(!StringUtils.startsWith(this.getIpPort(), "http")) {

            return "http://" + this.getIpPort();
        }
        return this.getIpPort();
    }
}

