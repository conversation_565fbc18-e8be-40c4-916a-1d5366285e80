package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.ApiLocationApiTagBO;

import java.util.List;

public interface ApiLocationApiTagService {

    void addApiLocationApiTag(ApiLocationApiTagBO apiLocationApiTagBO);

    void deleteApiLocationApiTag(ApiLocationApiTagBO apiLocationApiTagBO);

    void deleteByApiLocationId(Integer apiLocationId, String workspaceId);

    List<ApiLocationApiTagBO> getApiLocationApiTagList(ApiLocationApiTagBO apiLocationApiTagBO);

}
