package com.cmpay.hacp.dispatch.client;

import com.cmpay.hafr.agent.dto.*;
import com.cmpay.lemon.framework.data.DefaultDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static com.cmpay.hafr.agent.CommonConstants.API_CONFIG;
import static com.cmpay.hafr.agent.CommonConstants.API_REPORT;

public interface AgentNodeFeignClient {

    @PostMapping(API_CONFIG + "/update")
    @RequestLine("POST /v1/config/update")
    @Headers("Content-Type: application/json")
    DefaultRspDTO<NoBody> configUpdate(@RequestBody DefaultDTO<ConfigUpdateRequest> req);

    @PostMapping(API_CONFIG + "/status-change")
    @RequestLine("POST /v1/config/status-change")
    @Headers("Content-Type: application/json")
    DefaultRspDTO<NoBody> configStatusChange(@RequestBody DefaultDTO<ConfigStatusRequest> req);

    @PostMapping({"/v1/simulate/trace"})
    @RequestLine("POST /v1/simulate/trace")
    @Headers("Content-Type: application/json")
    DefaultRspDTO<TraceResult> simulateTrace(@RequestBody DefaultDTO<TraceRequest> req);


    @GetMapping(API_REPORT + "/dictionary")
    @RequestLine("GET /v1/report/dictionary")
    @Headers("Content-Type: application/json")
    DefaultRspDTO<DictionaryReport> reportDictionary();


}
