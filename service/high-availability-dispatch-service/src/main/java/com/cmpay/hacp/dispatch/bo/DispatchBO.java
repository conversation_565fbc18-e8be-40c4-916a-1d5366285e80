/*
 * @ClassName DispatchDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-28 09:42:23
 */
package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.DispatchDO;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hafr.agent.util.ConfigCheckUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


@Data
@EqualsAndHashCode(callSuper = true)
public class DispatchBO extends DispatchDO implements TenantCapable {

    private Set<String> apiLocationIds;

    private Set<String> apiTags;

    protected List<String> rules;
    /**
     * 生成调度配置使用
     */
    protected List<TypeValue> rulesList;

    protected List<DispatchRuleBO> ruleList;

    public void check(){
        if (StringUtils.isNotBlank(this.getDispatchName())
                && !ConfigCheckUtils.isValidParamName(this.getDispatchName())) {
            BusinessException.throwBusinessException("HFA00054");
        }

        if(StringUtils.isBlank(this.getDispatchDesc())) {
            BusinessException.throwBusinessException(DispatchMsgEnum.DISPATCH_DESC_NOT_BLANK);
        }

        List<String> rules = this.getRules();
        if (rules == null || rules.isEmpty()) {
            BusinessException.throwBusinessException("HFA00055");
        }

        rules.forEach((rule) -> {
            if (StringUtils.isBlank(rule)) {
                BusinessException.throwBusinessException("HFA00056");
            }
        });
        if(StringUtils.isBlank(this.getApiTag()) && StringUtils.isBlank(this.getApiLocationId())) {
            BusinessException.throwBusinessException(DispatchMsgEnum.DISPATCH_BIND_API_AND_RULE_NONE);
        }

        if(StringUtils.isNotBlank(this.getApiTag()) && StringUtils.isNotBlank(this.getApiLocationId())) {
            BusinessException.throwBusinessException(DispatchMsgEnum.DISPATCH_BIND_API_AND_RULE_BOTH);
        }



    }

    public void convertToRulesList(Map<Integer, String> ruleMap){
        this.rulesList=new ArrayList<>();
        if(StringUtils.isNotBlank(this.getApiRuleIds())) {
            List<String> list =  Arrays.stream(StringUtils.split(this.getApiRuleIds(), ",")).collect(Collectors.toList());
            list.forEach(f->{
                TypeValue typeValue = new TypeValue();
                typeValue.setType(f);
                typeValue.setValue(ruleMap.get(Integer.valueOf(f)));
                rulesList.add(typeValue);
            });
        }
        this.setRules(new ArrayList<>());
    }

    public List<String> convertRulesList(){
        if(JudgeUtils.isNotEmpty(rulesList)) {
            return rulesList.stream().map(TypeValue::getValue).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public void convertToString(){
        if(this.apiLocationIds != null) {
            this.setApiLocationId(StringUtils.join(this.apiLocationIds, ","));
        }
        if(this.apiTags != null) {
            this.setApiTag(StringUtils.join(this.apiTags, ","));
        }
        if(this.rules != null) {
            this.setApiRuleIds(StringUtils.join(this.rules, ","));
        }
    }

    public void convertToList(){
        if(this.getApiLocationId() != null) {
            this.setApiLocationIds(Arrays.stream(StringUtils.split(this.getApiLocationId(), ",")).collect(Collectors.toSet()));
        }
        if(this.getApiTag() != null) {
            this.setApiTags(Arrays.stream(StringUtils.split(this.getApiTag(), ",")).collect(Collectors.toSet()));
        }
        if(this.getApiRuleIds() != null) {
            this.setRules(Arrays.stream(StringUtils.split(this.getApiRuleIds(), ",")).collect(Collectors.toList()));
        }
    }

    public void convertApiLocationId(Map<String, Set<String>> apiTagMaps){

        if(apiLocationIds == null) {
            apiLocationIds = new HashSet<>();
        }

        if(StringUtils.isNotBlank(this.getApiLocationId())) {
            apiLocationIds.addAll(Arrays.asList(this.getApiLocationId().split(",")));
        }
        if(StringUtils.isNotBlank(this.getApiTag())) {
            for (String tag : this.getApiTag().split(",")) {
                if(apiTagMaps != null) {
                    Set<String> apiIds = apiTagMaps.getOrDefault(tag, new HashSet<>());
                    apiLocationIds.addAll(apiIds);
                }
            }
        }
    }
}