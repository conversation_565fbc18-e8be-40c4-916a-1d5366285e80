package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.AppInstanceMachineBO;
import com.cmpay.hacp.dispatch.dao.IAppInstanceMachineExtDao;
import com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO;
import com.cmpay.hacp.dispatch.service.AppInstanceMachineService;
import com.cmpay.hacp.dispatch.service.DispatchZoneBaseService;
import com.cmpay.hacp.tenant.service.WorkspaceBaseService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class AppInstanceMachineServiceImpl implements AppInstanceMachineService, DispatchZoneBaseService, WorkspaceBaseService {

    @Autowired
    private IAppInstanceMachineExtDao appInstanceMachineDao;
    @Override
    public void addAppInstanceMachine(AppInstanceMachineBO appInstanceMachineBO) {

        if(StringUtils.isNotBlank(appInstanceMachineBO.getAddress())) {
            appInstanceMachineDao.insert(appInstanceMachineBO);
        }
    }

    @Override
    public void updateAppInstanceMachine(AppInstanceMachineBO appInstanceMachineBO) {
        appInstanceMachineDao.update(appInstanceMachineBO);
    }

    @Override
    public void deleteAppInstanceMachine(AppInstanceMachineBO appInstanceMachineBO) {
        appInstanceMachineDao.delete(appInstanceMachineBO.getInstanceMachineId());
    }

    @Override
    public List<AppInstanceMachineBO> getAppInstanceMachineList(AppInstanceMachineBO appInstanceMachineBO) {
        return BeanConvertUtil.convertList(appInstanceMachineDao.find(appInstanceMachineBO), AppInstanceMachineBO.class);
    }

    @Override
    public Boolean existUseZoneByZoneId(Long zoneId) {
        AppInstanceMachineDO entity = new AppInstanceMachineDO();
        entity.setZoneId(zoneId);
        List<AppInstanceMachineDO> list = appInstanceMachineDao.find(entity);
        return JudgeUtils.isNotEmpty(list);
    }

    @Override
    public boolean whetherOrNotToAllowItemsToBeDeleted(String workspaceId) {
        AppInstanceMachineDO entity = new AppInstanceMachineDO();
        entity.setWorkspaceId(workspaceId);
        return JudgeUtils.isEmpty( appInstanceMachineDao.find(entity));
    }
}
