package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.DispatchZoneBO;
import com.cmpay.hacp.dispatch.dao.IDispatchZoneExtDao;
import com.cmpay.hacp.dispatch.entity.DispatchZoneDO;
import com.cmpay.hacp.dispatch.service.DispatchZoneBaseService;
import com.cmpay.hacp.dispatch.service.DispatchZoneService;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.tenant.service.HacpBaseService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/05/15 10:41
 * @since 1.0.0
 */
@Service
@Slf4j
public class DispatchZoneServiceImpl implements DispatchZoneService {

    @Resource
    private IDispatchZoneExtDao dispatchZoneExtDao;

    @Resource
    private HacpBaseService hacpBaseService;

    @Autowired(required = false)
    List<DispatchZoneBaseService> zoneDeleteServiceList;

    @Override
    public void add(DispatchZoneBO entity) {
        entity.check();
        hacpBaseService.workspaceExist(entity.getWorkspaceId());
        DispatchZoneDO dispatchZoneDO = existFind(entity);
        BeanUtils.copyProperties(dispatchZoneDO,entity);
        dispatchZoneDO.setCreateTime(LocalDateTime.now());
        dispatchZoneDO.setUpdateTime(LocalDateTime.now());
        dispatchZoneExtDao.insert(dispatchZoneDO);
    }

    private DispatchZoneDO existFind(DispatchZoneBO entity) {
        DispatchZoneDO dispatchZoneDO = new DispatchZoneDO();
        dispatchZoneDO.setId(entity.getId());
        dispatchZoneDO.setZoneLabel(entity.getZoneLabel());
        dispatchZoneDO.setWorkspaceId(entity.getWorkspaceId());
        List<DispatchZoneBO> dispatchZoneList = dispatchZoneExtDao.existFind(dispatchZoneDO);
        if (JudgeUtils.isNotEmpty(dispatchZoneList)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.ZONE_LABEL_EXISTS);
        }
        return dispatchZoneDO;
    }

    @Override
    public void update(DispatchZoneBO entity) {
        entity.check();
        hacpBaseService.workspaceExist(entity.getWorkspaceId());
        DispatchZoneDO dispatchZoneDO = existFind(entity);
        BeanUtils.copyProperties(dispatchZoneDO,entity);
        dispatchZoneDO.setUpdateTime(LocalDateTime.now());
        dispatchZoneExtDao.update(dispatchZoneDO);
    }

    @Override
    public void delete( Long id, String workspaceId) {
        DispatchZoneDO tenantZoneDO = new DispatchZoneDO();

        // 判断其他地方是否使用了机房
        if (JudgeUtils.isNotEmpty(zoneDeleteServiceList)) {
            zoneDeleteServiceList.forEach(zoneDeleteService -> {
                Boolean existed = zoneDeleteService.existUseZoneByZoneId(id);
                if (existed) {
                    log.error("zone in use,service impl class is : {}, id is : {}", zoneDeleteService.getClass().getName(),id);
                    BusinessException.throwBusinessException(DispatchMsgEnum.COMPUTER_ROOM_USE);
                }
            });
        }
        tenantZoneDO.setId(id);
        tenantZoneDO.setWorkspaceId(workspaceId);
        dispatchZoneExtDao.deleteTenantZone(tenantZoneDO);
    }

    @Override
    public DispatchZoneBO getInfo(String id, String workspaceId) {
        return BeanUtils.copyPropertiesReturnDest(new DispatchZoneBO(), dispatchZoneExtDao.getInfo(id, workspaceId));
    }

    @Override
    public PageInfo<DispatchZoneBO> getPageInfo(int pageNum, int pageSize, DispatchZoneBO entity) {
        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> dispatchZoneExtDao.getDetailList(BeanUtils.copyPropertiesReturnDest(new DispatchZoneDO(), entity)));
    }

    @Override
    public List<DispatchZoneBO> getSimpleList(String workspaceId) {
        DispatchZoneDO entity = new DispatchZoneDO();
        entity.setWorkspaceId(workspaceId);
        return dispatchZoneExtDao.getSimpleList(entity);
    }

    @Override
    public Map<Long, String> getSimpleMap(String workspaceId) {
        DispatchZoneDO entity = new DispatchZoneDO();
        entity.setWorkspaceId(workspaceId);
        List<DispatchZoneBO> simpleList = dispatchZoneExtDao.getSimpleList(entity);
        if(JudgeUtils.isEmpty(simpleList)){
            return new HashMap<>();
        }
        return simpleList.stream().collect(Collectors.toMap(DispatchZoneBO::getId, DispatchZoneBO::getZoneLabel));
    }
}
