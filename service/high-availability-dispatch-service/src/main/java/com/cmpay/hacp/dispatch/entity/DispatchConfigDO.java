/*
 * @ClassName DispatchConfigDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class DispatchConfigDO extends BaseDO {
    /**
     * @Fields dispatchConfigId 调度配置ID
     */
    private Integer dispatchConfigId;
    /**
     * @Fields dispatchVersion 配置调度版本
     */
    private String dispatchVersion;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields isCurrentVersion DISABLE:否  ENABLE:是
     */
    private Byte isCurrentVersion;
    /**
     * @Fields pushTimes 推送次数
     */
    private Byte pushTimes;
    /**
     * @Fields pushStartTime 推送开始时间
     */
    private LocalDateTime pushStartTime;
    /**
     * @Fields pushEndTime 推送结束时间
     */
    private LocalDateTime pushEndTime;
    /**
     * @Fields configDesc 配置说明
     */
    private String configDesc;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields configHex 配置调度版本指纹
     */
    private String configHex;
    /**
     * @Fields appInstanceGroups 业务节点组列表
     */
    private String appInstanceGroups;
    /**
     * @Fields appServices 业务服务列表
     */
    private String appServices;
    /**
     * @Fields appApiLocations 业务接口列表
     */
    private String appApiLocations;
    /**
     * @Fields appRules 业务流量规则
     */
    private String appRules;
    /**
     * @Fields dispatches 调度列表
     */
    private String dispatches;
    /**
     * @Fields apiTagMap api标签map
     */
    private String apiTagMap;

    public Integer getDispatchConfigId() {
        return dispatchConfigId;
    }

    public void setDispatchConfigId(Integer dispatchConfigId) {
        this.dispatchConfigId = dispatchConfigId;
    }

    public String getDispatchVersion() {
        return dispatchVersion;
    }

    public void setDispatchVersion(String dispatchVersion) {
        this.dispatchVersion = dispatchVersion;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public Byte getIsCurrentVersion() {
        return isCurrentVersion;
    }

    public void setIsCurrentVersion(Byte isCurrentVersion) {
        this.isCurrentVersion = isCurrentVersion;
    }

    public Byte getPushTimes() {
        return pushTimes;
    }

    public void setPushTimes(Byte pushTimes) {
        this.pushTimes = pushTimes;
    }

    public LocalDateTime getPushStartTime() {
        return pushStartTime;
    }

    public void setPushStartTime(LocalDateTime pushStartTime) {
        this.pushStartTime = pushStartTime;
    }

    public LocalDateTime getPushEndTime() {
        return pushEndTime;
    }

    public void setPushEndTime(LocalDateTime pushEndTime) {
        this.pushEndTime = pushEndTime;
    }

    public String getConfigDesc() {
        return configDesc;
    }

    public void setConfigDesc(String configDesc) {
        this.configDesc = configDesc;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getConfigHex() {
        return configHex;
    }

    public void setConfigHex(String configHex) {
        this.configHex = configHex;
    }

    public String getAppInstanceGroups() {
        return appInstanceGroups;
    }

    public void setAppInstanceGroups(String appInstanceGroups) {
        this.appInstanceGroups = appInstanceGroups;
    }

    public String getAppServices() {
        return appServices;
    }

    public void setAppServices(String appServices) {
        this.appServices = appServices;
    }

    public String getAppApiLocations() {
        return appApiLocations;
    }

    public void setAppApiLocations(String appApiLocations) {
        this.appApiLocations = appApiLocations;
    }

    public String getAppRules() {
        return appRules;
    }

    public void setAppRules(String appRules) {
        this.appRules = appRules;
    }

    public String getDispatches() {
        return dispatches;
    }

    public void setDispatches(String dispatches) {
        this.dispatches = dispatches;
    }

    public String getApiTagMap() {
        return apiTagMap;
    }

    public void setApiTagMap(String apiTagMap) {
        this.apiTagMap = apiTagMap;
    }
}