/*
 * @ClassName AppInstanceZoneDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class AppInstanceZoneDO extends BaseDO {
    /**
     * @Fields instanceZoneId 业务节点机房ID
     */
    private Integer instanceZoneId;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields instanceGroupId 业务服务组ID
     */
    private Integer instanceGroupId;
    /**
     * @Fields zoneId 机房ID
     */
    private Long zoneId;
    /**
     * @Fields externalRouterAddress 机房分流地址
     */
    private String externalRouterAddress;
    /**
     * @Fields checkSwitch 0:禁用  1:启用
     */
    private Byte checkSwitch;
    /**
     * @Fields checkInterval 检查间隔(毫秒)
     */
    private Short checkInterval;
    /**
     * @Fields checkFall 连续失败次数
     */
    private Short checkFall;
    /**
     * @Fields checkRise 连续成功次数
     */
    private Short checkRise;
    /**
     * @Fields checkTimeout 检查超时时间(毫秒)
     */
    private Short checkTimeout;
    /**
     * @Fields checkPort 检查端口
     */
    private Short checkPort;
    /**
     * @Fields checkHttpSend Http检查报文
     */
    private String checkHttpSend;
    /**
     * @Fields checkHttpExpectAlive Http检查预期状态码
     */
    private String checkHttpExpectAlive;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Integer getInstanceZoneId() {
        return instanceZoneId;
    }

    public void setInstanceZoneId(Integer instanceZoneId) {
        this.instanceZoneId = instanceZoneId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public Integer getInstanceGroupId() {
        return instanceGroupId;
    }

    public void setInstanceGroupId(Integer instanceGroupId) {
        this.instanceGroupId = instanceGroupId;
    }

    public Long getZoneId() {
        return zoneId;
    }

    public void setZoneId(Long zoneId) {
        this.zoneId = zoneId;
    }

    public String getExternalRouterAddress() {
        return externalRouterAddress;
    }

    public void setExternalRouterAddress(String externalRouterAddress) {
        this.externalRouterAddress = externalRouterAddress;
    }

    public Byte getCheckSwitch() {
        return checkSwitch;
    }

    public void setCheckSwitch(Byte checkSwitch) {
        this.checkSwitch = checkSwitch;
    }

    public Short getCheckInterval() {
        return checkInterval;
    }

    public void setCheckInterval(Short checkInterval) {
        this.checkInterval = checkInterval;
    }

    public Short getCheckFall() {
        return checkFall;
    }

    public void setCheckFall(Short checkFall) {
        this.checkFall = checkFall;
    }

    public Short getCheckRise() {
        return checkRise;
    }

    public void setCheckRise(Short checkRise) {
        this.checkRise = checkRise;
    }

    public Short getCheckTimeout() {
        return checkTimeout;
    }

    public void setCheckTimeout(Short checkTimeout) {
        this.checkTimeout = checkTimeout;
    }

    public Short getCheckPort() {
        return checkPort;
    }

    public void setCheckPort(Short checkPort) {
        this.checkPort = checkPort;
    }

    public String getCheckHttpSend() {
        return checkHttpSend;
    }

    public void setCheckHttpSend(String checkHttpSend) {
        this.checkHttpSend = checkHttpSend;
    }

    public String getCheckHttpExpectAlive() {
        return checkHttpExpectAlive;
    }

    public void setCheckHttpExpectAlive(String checkHttpExpectAlive) {
        this.checkHttpExpectAlive = checkHttpExpectAlive;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}