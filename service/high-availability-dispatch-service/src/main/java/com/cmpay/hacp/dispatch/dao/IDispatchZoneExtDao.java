/*
 * @ClassName IDispatchzoneDao
 * @Description 
 * @version 1.0
 * @Date 2024-05-15 10:07:29
 */
package com.cmpay.hacp.dispatch.dao;

import com.cmpay.hacp.dispatch.bo.DispatchZoneBO;
import com.cmpay.hacp.dispatch.entity.DispatchZoneDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IDispatchZoneExtDao extends IDispatchZoneDao {

    int deleteTenantZone(DispatchZoneDO tenantZoneDO);

    List<DispatchZoneBO> getDetailList(DispatchZoneDO tenantZoneDO);

    List<DispatchZoneBO> existFind(DispatchZoneDO tenantZoneDO);

    List<DispatchZoneBO> getSimpleList(DispatchZoneDO entity);

    DispatchZoneDO getInfo(@Param("id") String id, @Param("workspaceId") String workspaceId);
}