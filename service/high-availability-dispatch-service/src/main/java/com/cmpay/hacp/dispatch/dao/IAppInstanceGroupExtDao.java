/*
 * @ClassName IAppInstanceGroupDao
 * @Description 
 * @version 1.0
 * @Date 2024-06-19 09:29:43
 */
package com.cmpay.hacp.dispatch.dao;

import com.cmpay.hacp.dispatch.bo.AppInstanceMachineBO;
import com.cmpay.hacp.dispatch.entity.AppInstanceGroupDO;
import com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IAppInstanceGroupExtDao extends IAppInstanceGroupDao {

    List<AppInstanceMachineBO> getInstanceGroupMachineList(AppInstanceMachineDO appInstanceMachineDO);


    List<AppInstanceGroupDO> existFind(AppInstanceGroupDO appInstanceMachineDO);
}