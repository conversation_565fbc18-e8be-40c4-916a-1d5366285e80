package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.ApiLocationApiTagBO;
import com.cmpay.hacp.dispatch.bo.ApiTagBO;
import com.cmpay.hacp.dispatch.bo.DispatchBO;
import com.cmpay.hacp.dispatch.dao.IApiTagDao;
import com.cmpay.hacp.dispatch.entity.ApiTagDO;
import com.cmpay.hacp.dispatch.service.ApiLocationApiTagService;
import com.cmpay.hacp.dispatch.service.ApiTagService;
import com.cmpay.hacp.dispatch.service.DispatchService;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApiTagServiceImpl implements ApiTagService {

    @Autowired
    private IApiTagDao apiTagDao;

    @Autowired
    private ApiLocationApiTagService apiLocationApiTagService;

    @Autowired
    private DispatchService dispatchService;

    @Override
    public ApiTagBO addApiTag(ApiTagBO apiTagBO) {
        if (StringUtils.isBlank(apiTagBO.getApiTag())) {
            apiTagBO.setApiTag(apiTagBO.getApiTagName());
        }
        if (StringUtils.isBlank(apiTagBO.getApiTag())) {
            BusinessException.throwBusinessException(MsgEnum.INCOMPLETE_PARAM);
        }
        ApiTagBO findBO = new ApiTagBO();
        findBO.setWorkspaceId(apiTagBO.getWorkspaceId());
        findBO.setApiTag(apiTagBO.getApiTag());
        List<ApiTagDO> apiTagDOS = apiTagDao.find(findBO);
        if (apiTagDOS == null || apiTagDOS.isEmpty()) {
            apiTagDao.insert(apiTagBO);
            apiTagDOS = apiTagDao.find(findBO);
        }
        return BeanConvertUtil.convert(apiTagDOS.get(0), ApiTagBO.class);

    }

    @Override
    public List<ApiTagBO> getApiTagList(ApiTagBO apiTagBO) {
        return BeanConvertUtil.convertList(apiTagDao.find(apiTagBO), ApiTagBO.class);
    }

    @Override
    public void deleteApiTag(ApiTagBO apiTagBO) {
        ApiLocationApiTagBO apiLocationApiTagBO = new ApiLocationApiTagBO();
        apiLocationApiTagBO.setApiTagId(apiTagBO.getApiTagId());
        List<ApiLocationApiTagBO> apiLocationApiTagList = apiLocationApiTagService.getApiLocationApiTagList(apiLocationApiTagBO);
        DispatchBO dispatchBO = new DispatchBO();
        dispatchBO.setApiTag(String.valueOf(apiTagBO.getApiTagId()));
        if (JudgeUtils.isNotEmpty(apiLocationApiTagList) || dispatchService.existBind(dispatchBO)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.API_TAG_USE);
        }
        List<ApiTagDO> apiTagDOS = apiTagDao.find(apiTagBO);
        if (JudgeUtils.isEmpty(apiTagDOS)) {
            BusinessException.throwBusinessException(MsgEnum.NOT_OPERATION_PERMISSION);
        }
        apiTagDao.delete(apiTagBO.getApiTagId());
    }
}
