package com.cmpay.hacp.tenant.dao;

import com.cmpay.hacp.tenant.bo.TenantUserBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ITenantUserExtDao extends ITenantUserDao {
    /**
     * 删除租户管理员
     *
     * @param tenantId 租户ID
     * @return
     */
    int deleteAdminTenantUserByTenantId(@Param("tenantId") String tenantId);

    /**
     * 删除租户成员
     *
     * @param tenantId 租户ID
     * @return
     */
    int deleteTenantUserByTenantId(@Param("tenantId") String tenantId);

    /**
     * 查询租户成员列表
     *
     * @param tenantId 租户ID
     * @return 租户成员列表
     */
    List<TenantUserBO> getTenantUsers(@Param("tenantId") String tenantId);

}
