package com.cmpay.hacp.tenant.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ITenantWorkspaceUserRoleExtDao extends ITenantWorkspaceUserRoleDao {

    /**
     * 删除项目成员角色
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return 成功、失败
     */
    int deleteWorkspaceUserRoleByWorkspaceId(@Param("workspaceId") String workspaceId, @Param("userId") String userId);

    /**
     * 查询项目成员角色ID列表
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return 项目成员角色ID列表
     */
    List<String> getUserWorkspaceRoleIds(@Param("workspaceId") String workspaceId, @Param("userId") String userId);

    /**
     * 删除角色下绑定的用户关联
     *
     * @param workspaceRoleId 角色ID
     * @return 成功、失败
     */
    int deleteUserByWorkspaceRoleId(@Param("workspaceRoleId") String workspaceRoleId);
}
