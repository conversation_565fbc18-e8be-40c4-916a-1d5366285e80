/*
 * @ClassName ActRuTaskDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-20 10:14:01
 */
package com.cmpay.hacp.tenant.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class ActRuTaskDO extends BaseDO {
    /**
     * @Fields id 
     */
    private String id;
    /**
     * @Fields rev 
     */
    private Integer rev;
    /**
     * @Fields executionId 
     */
    private String executionId;
    /**
     * @Fields procInstId 
     */
    private String procInstId;
    /**
     * @Fields procDefId 
     */
    private String procDefId;
    /**
     * @Fields caseExecutionId 
     */
    private String caseExecutionId;
    /**
     * @Fields caseInstId 
     */
    private String caseInstId;
    /**
     * @Fields caseDefId 
     */
    private String caseDefId;
    /**
     * @Fields name 
     */
    private String name;
    /**
     * @Fields parentTaskId 
     */
    private String parentTaskId;
    /**
     * @Fields description 
     */
    private String description;
    /**
     * @Fields taskDefKey 
     */
    private String taskDefKey;
    /**
     * @Fields owner 
     */
    private String owner;
    /**
     * @Fields assignee 
     */
    private String assignee;
    /**
     * @Fields delegation 
     */
    private String delegation;
    /**
     * @Fields priority 
     */
    private Integer priority;
    /**
     * @Fields createTime 
     */
    private LocalDateTime createTime;
    /**
     * @Fields lastUpdated 
     */
    private LocalDateTime lastUpdated;
    /**
     * @Fields dueDate 
     */
    private LocalDateTime dueDate;
    /**
     * @Fields followUpDate 
     */
    private LocalDateTime followUpDate;
    /**
     * @Fields suspensionState 
     */
    private Integer suspensionState;
    /**
     * @Fields tenantId 
     */
    private String tenantId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getRev() {
        return rev;
    }

    public void setRev(Integer rev) {
        this.rev = rev;
    }

    public String getExecutionId() {
        return executionId;
    }

    public void setExecutionId(String executionId) {
        this.executionId = executionId;
    }

    public String getProcInstId() {
        return procInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getCaseExecutionId() {
        return caseExecutionId;
    }

    public void setCaseExecutionId(String caseExecutionId) {
        this.caseExecutionId = caseExecutionId;
    }

    public String getCaseInstId() {
        return caseInstId;
    }

    public void setCaseInstId(String caseInstId) {
        this.caseInstId = caseInstId;
    }

    public String getCaseDefId() {
        return caseDefId;
    }

    public void setCaseDefId(String caseDefId) {
        this.caseDefId = caseDefId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(String parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTaskDefKey() {
        return taskDefKey;
    }

    public void setTaskDefKey(String taskDefKey) {
        this.taskDefKey = taskDefKey;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getDelegation() {
        return delegation;
    }

    public void setDelegation(String delegation) {
        this.delegation = delegation;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getFollowUpDate() {
        return followUpDate;
    }

    public void setFollowUpDate(LocalDateTime followUpDate) {
        this.followUpDate = followUpDate;
    }

    public Integer getSuspensionState() {
        return suspensionState;
    }

    public void setSuspensionState(Integer suspensionState) {
        this.suspensionState = suspensionState;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}