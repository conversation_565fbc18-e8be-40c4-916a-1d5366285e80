<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.tenant.dao.ITenantWorkspaceRoleDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.entity.TenantWorkspaceRoleDO">
        <id column="workspace_role_id" property="workspaceRoleId" jdbcType="VARCHAR"/>
        <result column="workspace_role_name" property="workspaceRoleName" jdbcType="VARCHAR"/>
        <result column="workspace_role_type" property="workspaceRoleType" jdbcType="VARCHAR"/>
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        workspace_role_id, workspace_role_name, workspace_role_type, workspace_id, create_user,
        create_time, update_user, update_time, remarks, status
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from tenant_workspace_role
        where workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from tenant_workspace_role
        where workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.tenant.entity.TenantWorkspaceRoleDO">
        insert into tenant_workspace_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workspaceRoleId != null">
                workspace_role_id,
            </if>
            <if test="workspaceRoleName != null">
                workspace_role_name,
            </if>
            <if test="workspaceRoleType != null">
                workspace_role_type,
            </if>
            <if test="workspaceId != null">
                workspace_id,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workspaceRoleId != null">
                #{workspaceRoleId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceRoleName != null">
                #{workspaceRoleName,jdbcType=VARCHAR},
            </if>
            <if test="workspaceRoleType != null">
                #{workspaceRoleType,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null">
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.tenant.entity.TenantWorkspaceRoleDO">
        update tenant_workspace_role
        <set>
            <if test="workspaceRoleName != null">
                workspace_role_name = #{workspaceRoleName,jdbcType=VARCHAR},
            </if>
            <if test="workspaceRoleType != null">
                workspace_role_type = #{workspaceRoleType,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null">
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.tenant.entity.TenantWorkspaceRoleDO">
        select
        <include refid="Base_Column_List"/>
        from tenant_workspace_role
        <where>
            <if test="workspaceRoleId != null">
                and workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceRoleName != null">
                and workspace_role_name = #{workspaceRoleName,jdbcType=VARCHAR}
            </if>
            <if test="workspaceRoleType != null">
                and workspace_role_type = #{workspaceRoleType,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null">
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null">
                and remarks = #{remarks,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>