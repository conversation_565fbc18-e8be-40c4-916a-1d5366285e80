<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.tenant.dao.ITenantUserExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.bo.TenantUserBO">
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_user_type" property="tenantUserType" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="user_status" property="userStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , tenant_id, user_id, tenant_user_type, create_user, create_time, update_user,
        update_time, remarks, status
    </sql>

    <delete id="deleteAdminTenantUserByTenantId">
        delete
        from tenant_user
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and tenant_user_type = '1'
    </delete>

    <select id="getTenantUsers" resultMap="BaseResultMap">
        SELECT t0.tenant_id,
               t0.tenant_name,
               t3.user_id,
               t3.user_name,
               t3.full_name,
               t3.email,
               t3.mobile,
               t3.status                               AS user_status,
               IF(t4.tenant_user_type = '1', '1', '0') AS tenant_user_type
        FROM tenant t0
                 LEFT JOIN tenant_workspace t1 ON t1.tenant_id = t0.tenant_id
                 LEFT JOIN tenant_workspace_user t2 ON t1.workspace_id = t2.workspace_id
                 LEFT JOIN tenant_user t4 ON t4.tenant_id = t1.tenant_id
                 LEFT JOIN sys_user t3 ON (t2.user_id = t3.user_id or t4.user_id = t3.user_id)
        where t0.tenant_id = #{tenantId,jdbcType=VARCHAR}
        order by t3.create_time asc
    </select>

    <delete id="deleteTenantUserByTenantId">
        delete
        from tenant_user
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </delete>
</mapper>
