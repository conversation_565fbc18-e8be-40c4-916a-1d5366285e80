<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.tenant.dao.IWorkspaceDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.entity.WorkspaceDO">
        <id column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="workspace_name" property="workspaceName" jdbcType="VARCHAR"/>
        <result column="profile" property="profile" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        workspace_id, workspace_name,profile, create_user, create_time, update_user, update_time,
        remarks, status
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from workspace
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from workspace
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.tenant.entity.WorkspaceDO">
        insert into workspace
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workspaceId != null">
                workspace_id,
            </if>
            <if test="workspaceName != null">
                workspace_name,
            </if>
            <if test="profile != null">
                profile,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workspaceId != null">
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceName != null">
                #{workspaceName,jdbcType=VARCHAR},
            </if>
            <if test="profile != null">
                #{profile,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.tenant.entity.WorkspaceDO">
        update workspace
        <set>
            <if test="workspaceName != null">
                workspace_name = #{workspaceName,jdbcType=VARCHAR},
            </if>
            <if test="profile != null">
                profile = #{profile,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.tenant.entity.WorkspaceDO">
        select
        <include refid="Base_Column_List"/>
        from workspace
        <where>
            <if test="workspaceId != null">
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceName != null">
                and workspace_name = #{workspaceName,jdbcType=VARCHAR}
            </if>
            <if test="profile != null">
                and profile = #{profile,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null">
                and remarks = #{remarks,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>