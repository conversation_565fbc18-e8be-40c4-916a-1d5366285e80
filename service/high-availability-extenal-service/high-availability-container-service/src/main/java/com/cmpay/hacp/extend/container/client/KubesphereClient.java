package com.cmpay.hacp.extend.container.client;

import com.cmpay.hacp.extend.container.client.dto.*;
import feign.FeignException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 后续可能切换成接口调用，不使用rest调用容器接口
 *
 * <AUTHOR>
 * @create 2024/10/12 14:11
 * @since 1.0.0
 */
@FeignClient(url = "${hacp.emergence.kubesphere.properties.url:}", name = "kubesphere", contextId = "kubesphereClient")
public interface KubesphereClient {

    @PostMapping(value = "/oauth/token", headers = "Content-Type=application/x-www-form-urlencoded")
    KubesphereTokenRspDTO getToken(@RequestBody KubesphereTokenReqDTO reqDTO);

    @GetMapping("/kapis/clusters/{cluster}/tenant.kubesphere.io/v1alpha2/namespaces")
    KubesphereRspDTO getNs(@PathVariable("cluster") String cluster, @RequestHeader("Authorization") String token);

    @GetMapping("/kapis/tenant.kubesphere.io/v1alpha2/workspaces")
    KubesphereRspDTO getWorkspaces(@RequestHeader("Authorization") String token);

    @GetMapping("/kapis/tenant.kubesphere.io/v1alpha3/workspacetemplates/{workspace}")
    KubesphereClusterRspDTO getClusters(@PathVariable("workspace") String workspace, @RequestHeader("Authorization") String token);

    @GetMapping("/api/clusters/{cluster}/v1/namespaces/{namespace}/pods")
    KubesphereNsPodsDTO getNsPods(@PathVariable("cluster") String cluster,
            @PathVariable("namespace") String namespace,
            @RequestHeader("Authorization") String token);

    @DeleteMapping("/api/clusters/{cluster}/v1/namespaces/{namespace}/pods/{pod}")
    String deletePod(@PathVariable("cluster") String cluster,
            @PathVariable("namespace") String namespace,
            @PathVariable("pod") String pod,
            @RequestHeader("Authorization") String token) throws FeignException;
}
