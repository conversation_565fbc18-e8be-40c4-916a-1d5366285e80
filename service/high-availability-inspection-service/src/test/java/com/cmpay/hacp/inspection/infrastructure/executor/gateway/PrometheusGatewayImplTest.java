package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusQueryResponse;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusToken;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusTokenRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusTokenResponse;
import com.cmpay.hacp.inspection.infrastructure.executor.plugin.config.PrometheusProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PrometheusGatewayImpl单元测试
 * 
 * 注意：这是一个基础的单元测试示例
 * 实际使用时需要配置真实的Prometheus服务器或使用Mock服务器
 */
@ExtendWith(MockitoExtension.class)
class PrometheusGatewayImplTest {
    
    private PrometheusGatewayImpl prometheusGateway;
    private PrometheusProperties properties;
    
    @BeforeEach
    void setUp() {
        // 创建测试配置
        properties = new PrometheusProperties();
        properties.setBaseUrl("http://localhost:9090");
        
        // 配置认证
        PrometheusProperties.Auth auth = new PrometheusProperties.Auth();
        auth.setBaseUrl("http://localhost:8080");
        auth.setUserId("test-user");
        auth.setUserKey("test-password");
        auth.setTokenCacheMinutes(60);
        properties.setAuth(auth);
        
        // 配置HTTP
        PrometheusProperties.Http http = new PrometheusProperties.Http();
        http.setConnectTimeout(5000);
        http.setReadTimeout(10000);
        http.setWriteTimeout(10000);
        http.setMaxIdleConnections(5);
        http.setKeepAliveDuration(2);
        http.setEnableSslCheck(false); // 测试环境禁用SSL验证
        properties.setHttp(http);
        
        prometheusGateway = new PrometheusGatewayImpl(properties);
    }
    
    @Test
    void testPrometheusTokenCreation() {
        // 测试PrometheusToken的创建和方法
        LocalDateTime now = LocalDateTime.now();
        PrometheusToken token = PrometheusToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .createdAt(now)
                .expiresAt(now.plusMinutes(60))
                .build();
        
        assertNotNull(token);
        assertEquals("test-token", token.getAccessToken());
        assertEquals("Bearer", token.getTokenType());
        assertEquals("Bearer test-token", token.getAuthorizationHeader());
        assertFalse(token.isExpired());
        assertFalse(token.isExpiringSoon());
    }
    
    @Test
    void testPrometheusTokenExpiration() {
        // 测试token过期逻辑
        LocalDateTime now = LocalDateTime.now();
        PrometheusToken expiredToken = PrometheusToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .createdAt(now.minusHours(2))
                .expiresAt(now.minusHours(1))
                .build();
        
        assertTrue(expiredToken.isExpired());
        assertTrue(expiredToken.isExpiringSoon());
    }
    
    @Test
    void testPrometheusQueryRequestCreation() {
        // 测试查询请求的创建
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("step", "15s");
        
        PrometheusQueryRequest request = PrometheusQueryRequest.builder()
                .query("up")
                .time(LocalDateTime.now())
                .timeout(30)
                .additionalParams(additionalParams)
                .build();
        
        assertNotNull(request);
        assertEquals("up", request.getQuery());
        assertEquals(30, request.getTimeout());
        assertNotNull(request.getTimeUnix());
        assertEquals("15s", request.getAdditionalParams().get("step"));
    }
    
    @Test
    void testPrometheusQueryResponseCreation() {
        // 测试查询响应的创建
        PrometheusQueryResponse.PrometheusResult result = PrometheusQueryResponse.PrometheusResult.builder()
                .metric(Map.of("__name__", "up", "instance", "localhost:9090"))
                .value(java.util.List.of(1640995200.0, "1"))
                .build();
        
        PrometheusQueryResponse.PrometheusData data = PrometheusQueryResponse.PrometheusData.builder()
                .resultType("vector")
                .result(java.util.List.of(result))
                .build();
        
        PrometheusQueryResponse response = PrometheusQueryResponse.builder()
                .status("success")
                .data(data)
                .build();
        
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("vector", response.getData().getResultType());
        assertEquals(1, response.getData().getResult().size());
        assertEquals("1", response.getData().getResult().get(0).getMetricValue());
        assertNotNull(response.getData().getResult().get(0).getMetricTimestamp());
    }

    @Test
    void testPrometheusTokenRequestCreation() {
        // 测试token请求的创建
        PrometheusTokenRequest request = PrometheusTokenRequest.builder()
                .userId("test-user")
                .userKey("test-password")
                .build();

        assertNotNull(request);
        assertEquals("test-user", request.getUserId());
        assertEquals("test-password", request.getUserKey());
    }

    @Test
    void testPrometheusTokenResponseSuccess() {
        // 测试成功的token响应
        PrometheusTokenResponse response = new PrometheusTokenResponse();
        response.setToken("test-token-123");
        response.setUserId("test-user");
        response.setExpire(3600L);
        response.setMsgCd("FFM00000");
        response.setMsgInfo("");

        assertTrue(response.isSuccess());
        assertFalse(response.isAuthError());
        assertFalse(response.isUserStatusError());
        assertEquals("test-token-123", response.getToken());
        assertEquals("test-user", response.getUserId());
        assertEquals(3600L, response.getExpire());
    }

    @Test
    void testPrometheusTokenResponseAuthError() {
        // 测试认证错误的token响应
        PrometheusTokenResponse response = new PrometheusTokenResponse();
        response.setMsgCd("FFM80001");
        response.setMsgInfo("账户或密码错误");

        assertFalse(response.isSuccess());
        assertTrue(response.isAuthError());
        assertFalse(response.isUserStatusError());
    }

    @Test
    void testPrometheusTokenResponseUserStatusError() {
        // 测试用户状态异常的token响应
        PrometheusTokenResponse response = new PrometheusTokenResponse();
        response.setMsgCd("FFM80002");
        response.setMsgInfo("用户状态异常");

        assertFalse(response.isSuccess());
        assertFalse(response.isAuthError());
        assertTrue(response.isUserStatusError());
    }
    
    /**
     * 集成测试示例
     * 需要真实的Prometheus服务器才能运行
     * 在实际环境中取消注释并配置正确的服务器地址
     */
    // @Test
    // void testRealPrometheusQuery() throws Exception {
    //     // 初始化网关
    //     prometheusGateway.afterPropertiesSet();
    //     
    //     try {
    //         // 创建查询请求
    //         PrometheusQueryRequest request = PrometheusQueryRequest.builder()
    //                 .query("up")
    //                 .timeout(30)
    //                 .build();
    //         
    //         // 执行查询
    //         PrometheusQueryResponse response = prometheusGateway.query(request);
    //         
    //         // 验证响应
    //         assertNotNull(response);
    //         assertTrue(response.isSuccess());
    //         assertNotNull(response.getData());
    //         
    //     } finally {
    //         // 清理资源
    //         prometheusGateway.destroy();
    //     }
    // }
}
