package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.application.service.PrometheusIndicatorMetadataService;
import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.infrastructure.executor.plugin.config.PrometheusProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Prometheus指标查询测试
 */
@ExtendWith(MockitoExtension.class)
class PrometheusIndicatorQueryTest {
    
    @Mock
    private PrometheusIndicatorMetadataService metadataService;
    
    private PrometheusGatewayImpl prometheusGateway;
    private PrometheusProperties properties;
    
    @BeforeEach
    void setUp() {
        // 创建测试配置
        properties = new PrometheusProperties();
        properties.setBaseUrl("http://localhost:8080");
        
        // 配置认证
        PrometheusProperties.Auth auth = new PrometheusProperties.Auth();
        auth.setBaseUrl("http://localhost:8080");
        auth.setUserId("test-user");
        auth.setUserKey("test-password");
        auth.setTokenCacheMinutes(60);
        properties.setAuth(auth);
        
        // 配置HTTP
        PrometheusProperties.Http http = new PrometheusProperties.Http();
        http.setConnectTimeout(5000);
        http.setReadTimeout(10000);
        http.setWriteTimeout(10000);
        http.setMaxIdleConnections(5);
        http.setKeepAliveDuration(2);
        http.setEnableSslCheck(false);
        properties.setHttp(http);
        
        prometheusGateway = new PrometheusGatewayImpl(properties, metadataService);
    }
    
    @Test
    void testIndicatorQueryRequestCreation() {
        // 测试指标查询请求的创建
        LocalDateTime startTime = LocalDateTime.now().minusHours(1);
        LocalDateTime endTime = LocalDateTime.now();
        
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(startTime)
                .endTime(endTime)
                .indicatorType("mid")
                .indicatorName("middleware_nginx_readconnect")
                .pageNum(1)
                .pageSize(100)
                .zones(Arrays.asList("changsha", "zhezhong"))
                .specialNameOne("web-app")
                .specialNameTwo("nginx-01")
                .specialNameThree("nginx")
                .specialNameFour("changsha")
                .build();
        
        assertNotNull(request);
        assertEquals("mid", request.getIndicatorType());
        assertEquals("middleware_nginx_readconnect", request.getIndicatorName());
        assertEquals(1, request.getPageNum());
        assertEquals(100, request.getPageSize());
        assertEquals(2, request.getZones().size());
        assertEquals("web-app", request.getSpecialNameOne());
        
        // 测试特殊字段Map
        Map<String, String> specialFields = request.getSpecialFields();
        assertEquals("web-app", specialFields.get("specialNameOne"));
        assertEquals("nginx-01", specialFields.get("specialNameTwo"));
        assertEquals("nginx", specialFields.get("specialNameThree"));
        assertEquals("changsha", specialFields.get("specialNameFour"));
    }
    
    @Test
    void testIndicatorQueryResponseCreation() {
        // 测试指标查询响应的创建
        IndicatorQueryResponse.IndicatorData data1 = IndicatorQueryResponse.IndicatorData.builder()
                .indicatorName("middleware_nginx_readconnect")
                .timestamp(LocalDateTime.now())
                .value("100")
                .zone("changsha")
                .one("web-app")
                .two("nginx-01")
                .three("nginx")
                .four("changsha")
                .build();
        
        IndicatorQueryResponse.IndicatorData data2 = IndicatorQueryResponse.IndicatorData.builder()
                .indicatorName("middleware_nginx_readconnect")
                .timestamp(LocalDateTime.now())
                .value("150")
                .zone("zhezhong")
                .one("web-app")
                .two("nginx-02")
                .three("nginx")
                .four("zhezhong")
                .build();
        
        IndicatorQueryResponse response = IndicatorQueryResponse.builder()
                .msgCd("FFM00000")
                .msgInfo("")
                .data(Arrays.asList(data1, data2))
                .total(2L)
                .pageNum(1)
                .pageSize(100)
                .build();
        
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(2, response.getData().size());
        assertEquals(2L, response.getTotal());
        assertEquals("100", response.getData().get(0).getValue());
        assertEquals("150", response.getData().get(1).getValue());
    }
    
    @Test
    void testIndicatorDefinitionCreation() {
        // 测试指标定义的创建
        IndicatorDefinition.IndicatorParam param1 = IndicatorDefinition.IndicatorParam.builder()
                .paramOrder(1)
                .paramName("appName")
                .paramCode("specialNameOne")
                .paramType("STRING")
                .isRequired(true)
                .description("应用名称")
                .build();
        
        IndicatorDefinition.IndicatorParam param2 = IndicatorDefinition.IndicatorParam.builder()
                .paramOrder(2)
                .paramName("instance")
                .paramCode("specialNameTwo")
                .paramType("STRING")
                .isRequired(true)
                .description("实例名称")
                .build();
        
        IndicatorDefinition definition = IndicatorDefinition.builder()
                .indicatorId("0000000501")
                .indicatorName("nginx读取连接数")
                .indicatorEnglishName("middleware_nginx_readconnect")
                .groupCode("nginx")
                .indicatorType("mid")
                .description("Nginx读取连接数指标")
                .status(1)
                .inputParams(Arrays.asList(param1, param2))
                .build();
        
        assertNotNull(definition);
        assertEquals("0000000501", definition.getIndicatorId());
        assertEquals("nginx读取连接数", definition.getIndicatorName());
        assertEquals("middleware_nginx_readconnect", definition.getIndicatorEnglishName());
        assertEquals("nginx", definition.getGroupCode());
        assertEquals("mid", definition.getIndicatorType());
        assertEquals(2, definition.getInputParams().size());
        
        // 测试参数
        IndicatorDefinition.IndicatorParam firstParam = definition.getInputParams().get(0);
        assertEquals(1, firstParam.getParamOrder());
        assertEquals("appName", firstParam.getParamName());
        assertEquals("specialNameOne", firstParam.getParamCode());
        assertTrue(firstParam.getIsRequired());
    }
    
    @Test
    void testParameterValidation() {
        // 模拟指标定义
        IndicatorDefinition.IndicatorParam param1 = IndicatorDefinition.IndicatorParam.builder()
                .paramOrder(1)
                .paramName("appName")
                .paramCode("specialNameOne")
                .isRequired(true)
                .build();
        
        IndicatorDefinition definition = IndicatorDefinition.builder()
                .indicatorEnglishName("middleware_nginx_readconnect")
                .inputParams(Arrays.asList(param1))
                .build();
        
        when(metadataService.getIndicatorDefinition("middleware_nginx_readconnect"))
                .thenReturn(definition);
        when(metadataService.validateIndicatorParams(anyString(), any(Map.class)))
                .thenReturn(true);
        
        // 测试参数验证
        Map<String, String> validParams = Map.of("specialNameOne", "web-app");
        boolean isValid = metadataService.validateIndicatorParams("middleware_nginx_readconnect", validParams);
        assertTrue(isValid);
    }
    
    /**
     * 集成测试示例
     * 需要真实的Prometheus服务器才能运行
     * 在实际环境中取消注释并配置正确的服务器地址
     */
    // @Test
    // void testRealIndicatorQuery() throws Exception {
    //     // 初始化网关
    //     prometheusGateway.afterPropertiesSet();
    //     
    //     try {
    //         // 创建指标查询请求
    //         IndicatorQueryRequest request = IndicatorQueryRequest.builder()
    //                 .startTime(LocalDateTime.now().minusMinutes(30))
    //                 .endTime(LocalDateTime.now())
    //                 .indicatorType("mid")
    //                 .indicatorName("middleware_nginx_state")
    //                 .pageNum(1)
    //                 .pageSize(10)
    //                 .specialNameOne("web-app")
    //                 .specialNameTwo("nginx-01")
    //                 .specialNameThree("nginx")
    //                 .specialNameFour("changsha")
    //                 .build();
    //         
    //         // 执行查询
    //         IndicatorQueryResponse response = prometheusGateway.getIndicatorDataList(request);
    //         
    //         // 验证响应
    //         assertNotNull(response);
    //         assertNotNull(response.getMsgCd());
    //         
    //         if (response.isSuccess()) {
    //             assertNotNull(response.getData());
    //             System.out.println("Query successful, data count: " + response.getData().size());
    //         } else {
    //             System.out.println("Query failed: " + response.getMsgInfo());
    //         }
    //         
    //     } finally {
    //         // 清理资源
    //         prometheusGateway.destroy();
    //     }
    // }
}
