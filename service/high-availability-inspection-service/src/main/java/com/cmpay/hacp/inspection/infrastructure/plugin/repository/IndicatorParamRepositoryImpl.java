package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.repository.IndicatorParamRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.IndicatorParamConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PrometheusIndicatorParamDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PrometheusIndicatorParamMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class IndicatorParamRepositoryImpl extends CrudRepository<PrometheusIndicatorParamMapper, PrometheusIndicatorParamDO> implements IndicatorParamRepository {
    private final IndicatorParamConverter indicatorParamConverter;

    @Override
    public IndicatorDefinition getIndicatorParams(IndicatorDefinition indicatorDefinition) {
        List<PrometheusIndicatorParamDO> indicatorParamDOList = list(Wrappers.lambdaQuery(PrometheusIndicatorParamDO.class)
                .eq(PrometheusIndicatorParamDO::getIndicatorId, indicatorDefinition.getIndicatorId()));
        indicatorDefinition.setInputParams(indicatorParamConverter.toIndicatorParamList(indicatorParamDOList));
        return indicatorDefinition;
    }
}
