package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;

import java.util.List;

/**
 * Prometheus指标元数据服务接口
 */
public interface PrometheusIndicatorMetadataService {
    
    /**
     * 根据指标英文名查询指标定义
     * 
     * @param indicatorEnglishName 指标英文名
     * @return 指标定义
     */
    IndicatorDefinition getIndicatorDefinition(String indicatorEnglishName);
    
    /**
     * 根据指标类型查询指标定义列表
     * 
     * @param indicatorType 指标类型
     * @return 指标定义列表
     */
    List<IndicatorDefinition> getIndicatorDefinitionsByType(String indicatorType);
    
    /**
     * 根据指标组代码查询指标定义列表
     * 
     * @param groupCode 指标组代码
     * @return 指标定义列表
     */
    List<IndicatorDefinition> getIndicatorDefinitionsByGroup(String groupCode);
    
    /**
     * 验证指标查询请求参数
     * 
     * @param indicatorEnglishName 指标英文名
     * @param specialFields 特殊字段Map
     * @return 验证结果，true表示验证通过
     */
    boolean validateIndicatorParams(String indicatorEnglishName, java.util.Map<String, String> specialFields);
}
