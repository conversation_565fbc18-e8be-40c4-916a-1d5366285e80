package com.cmpay.hacp.inspection.application.plugin;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.service.IndicatorService;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 指标元数据服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorMetadataServiceImpl implements IndicatorMetadataService {
    private final IndicatorService indicatorService;


    @Override
    public List<IndicatorDefinition> getIndicatorDefinitionsByType(IndicatorType indicatorType) {
        return indicatorService.getIndicatorDefinitionsByType(indicatorType);
    }

    @Override
    public IndicatorDefinition getIndicatorParam(String indicatorId) {
        return indicatorService.getIndicatorDefinitionById(indicatorId);
    }
}
