package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptOutputFieldDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleConditionDO;

import java.util.Map;

/**
 * 规则匹配服务接口
 * 用于匹配巡检结果与规则条件
 */
public interface RuleMatchingService {

    /**
     * 匹配巡检结果与规则条件
     *
     * @param ruleId 规则ID
     * @param pluginId 插件ID
     * @param scriptOutput 脚本输出内容
     * @return 规则匹配结果
     */
    RuleMatchingResult matchRule(String ruleId, String pluginId, String scriptOutput);

    /**
     * 匹配解析后的字段值与规则条件
     *
     * @param parsedValues 解析后的字段值
     * @param ruleCondition 规则条件
     * @param fieldDefinition 字段定义
     * @return 规则匹配结果
     */
    RuleMatchingResult matchParsedValues(Map<String, Object> parsedValues, 
                                       RuleConditionDO ruleCondition,
                                       PluginScriptOutputFieldDO fieldDefinition);

    /**
     * 评估单个字段值是否满足规则条件
     *
     * @param fieldValue 字段值
     * @param ruleCondition 规则条件
     * @return 是否满足条件
     */
    boolean evaluateFieldCondition(Object fieldValue, RuleConditionDO ruleCondition);
}
