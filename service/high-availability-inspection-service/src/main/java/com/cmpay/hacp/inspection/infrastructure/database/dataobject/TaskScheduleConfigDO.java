package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.IntervalUnit;
import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("inspection_task_schedule")
public class TaskScheduleConfigDO extends BaseDO{
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 调度类型
     */
    private ScheduleType scheduleType;

    /**
     * 是否启用定时执行
     */
    private boolean enabled;

    /**
     * CRON 表达式 (CRON类型专用)
     */
    private String cronExpression;

    /**
     * 间隔值 (INTERVAL类型专用)
     */
    private Integer intervalValue;

    /**
     * 间隔单位 (INTERVAL类型专用)
     */
    private IntervalUnit intervalUnit;

    /**
     * 执行日期 (FIXED_TIME类型专用)
     */
    private LocalDate executionDate;

    /**
     * 执行时间 (FIXED_TIME类型专用)
     */
    private LocalTime executionTime;
}
