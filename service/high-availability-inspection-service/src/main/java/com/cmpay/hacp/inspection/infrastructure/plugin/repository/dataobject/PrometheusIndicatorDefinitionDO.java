package com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;
import lombok.Data;

/**
 * Prometheus指标定义数据对象
 */
@Data
@TableName("prometheus_indicator_definition")
public class PrometheusIndicatorDefinitionDO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 指标ID
     */
    private String indicatorId;
    
    /**
     * 指标名
     */
    private String indicatorName;
    
    /**
     * 指标类型：mid-中间件，host-主机，container-容器
     */
    private IndicatorType indicatorType;
    
    /**
     * 指标描述
     */
    private String description;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
}
