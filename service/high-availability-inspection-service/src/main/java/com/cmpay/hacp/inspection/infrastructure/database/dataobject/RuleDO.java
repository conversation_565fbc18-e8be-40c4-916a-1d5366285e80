package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.RuleLevelEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleStatusEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检规则实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule")
public class RuleDO extends BaseDO {

    /**
     * 规则ID
     */
    private String ruleId;

    /**
      * 规则名称（非空）
    */
    @TableField("name") // 显式映射字段（可省略，默认驼峰映射）
    private String name;

    /**
     * 规则描述（允许为空）
     */
    @TableField("description") // 建议重命名为非关键字字段（如 description）
    private String description;

    /**
     * 告警等级（低、中、高、严重）
     */
    private RuleLevelEnum level;

    /**
     * 规则状态（0启用，1禁用）
     */
    private RuleStatusEnum status;

    /**
     * 规则类型（指标、日志、可用性）
     */
    private RuleType type;

}
