package com.cmpay.hacp.inspection.infrastructure.executor.plugin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "inspection.ssh")
@Data
public class SshProperties {
    private int connectionTimeout = 5000;
    private int executionTimeout = 30000;
    private int maxConnections = 10;
    private String defaultUsername;
    private String defaultPrivateKeyPath;
}
