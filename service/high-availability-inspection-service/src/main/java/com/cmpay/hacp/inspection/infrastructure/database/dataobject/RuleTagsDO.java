package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则标签关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("rule_tags")
public class RuleTagsDO extends BaseDO {
    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 标签ID
     */
    private Long tagId;
}
