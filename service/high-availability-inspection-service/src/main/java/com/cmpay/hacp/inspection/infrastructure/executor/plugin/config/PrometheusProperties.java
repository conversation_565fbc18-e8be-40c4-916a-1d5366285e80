package com.cmpay.hacp.inspection.infrastructure.executor.plugin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Prometheus配置属性
 */
@ConfigurationProperties(prefix = "inspection.prometheus")
@Data
public class PrometheusProperties {
    
    /**
     * Prometheus服务器基础URL
     */
    private String baseUrl = "http://localhost:9090";
    
    /**
     * 认证相关配置
     */
    private Auth auth = new Auth();
    
    /**
     * HTTP连接配置
     */
    private Http http = new Http();
    
    /**
     * 认证配置
     */
    @Data
    public static class Auth {

        /**
         * 认证服务器基础URL（承载地址:连接端口）
         */
        private String baseUrl;

        /**
         * 用户ID
         */
        private String userId;

        /**
         * 用户密码（userKey）
         */
        private String userKey;

        /**
         * token缓存时间（分钟），默认60分钟
         */
        private int tokenCacheMinutes = 60;

        /**
         * 获取完整的token URL
         *
         * @return 完整的token获取URL
         */
        public String getTokenUrl() {
            return baseUrl + "/openability/v1/firefly/getToken";
        }
    }
    
    /**
     * HTTP连接配置
     */
    @Data
    public static class Http {
        
        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 10000;
        
        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeout = 30000;
        
        /**
         * 写入超时时间（毫秒）
         */
        private int writeTimeout = 30000;
        
        /**
         * 最大空闲连接数
         */
        private int maxIdleConnections = 10;
        
        /**
         * 连接保持活跃时间（分钟）
         */
        private int keepAliveDuration = 5;
        
        /**
         * 是否启用SSL验证
         */
        private boolean enableSslCheck = true;
    }
}
