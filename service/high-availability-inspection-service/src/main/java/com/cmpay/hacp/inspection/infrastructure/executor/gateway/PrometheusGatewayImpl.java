package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.domain.executor.gateway.PrometheusGateway;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusQueryResponse;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusToken;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusTokenRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusTokenResponse;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.application.plugin.IndicatorMetadataService;
import com.cmpay.hacp.inspection.infrastructure.executor.plugin.config.PrometheusProperties;
import com.cmpay.hacp.inspection.infrastructure.config.PrometheusCacheConfig;
import com.cmpay.lemon.common.exception.BusinessException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import okhttp3.*;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * Prometheus网关实现类
 * 使用OkHttp客户端与Prometheus系统进行HTTP通信
 */
@Slf4j
@Component
@EnableConfigurationProperties(PrometheusProperties.class)
public class PrometheusGatewayImpl implements PrometheusGateway, InitializingBean, DisposableBean {

    private final PrometheusProperties properties;
    private final ObjectMapper objectMapper;
    private final IndicatorMetadataService metadataService;

    private OkHttpClient httpClient;

    public PrometheusGatewayImpl(PrometheusProperties properties,
                                IndicatorMetadataService metadataService) {
        this.properties = properties;
        this.objectMapper = new ObjectMapper();
        this.metadataService = metadataService;
    }

    @Override
    public void afterPropertiesSet() {
        initializeHttpClient();
        log.info("Prometheus gateway initialized with base URL: {}", properties.getBaseUrl());
    }

    @Override
    public void destroy() {
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
            log.info("Prometheus HTTP client has been gracefully shut down");
        }
    }

    /**
     * 初始化OkHttp客户端
     */
    private void initializeHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(properties.getHttp().getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(properties.getHttp().getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(properties.getHttp().getWriteTimeout(), TimeUnit.MILLISECONDS)
                .connectionPool(new ConnectionPool(
                        properties.getHttp().getMaxIdleConnections(),
                        properties.getHttp().getKeepAliveDuration(),
                        TimeUnit.MINUTES
                ));

        // 如果禁用SSL验证，配置信任所有证书
        if (!properties.getHttp().isEnableSslCheck()) {
            try {
                final TrustManager[] trustAllCerts = new TrustManager[]{
                        new X509TrustManager() {
                            @Override
                            public void checkClientTrusted(X509Certificate[] chain, String authType) {
                            }

                            @Override
                            public void checkServerTrusted(X509Certificate[] chain, String authType) {
                            }

                            @Override
                            public X509Certificate[] getAcceptedIssuers() {
                                return new X509Certificate[]{};
                            }
                        }
                };

                final SSLContext sslContext = SSLContext.getInstance("SSL");
                sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
                builder.sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0]);
                builder.hostnameVerifier((hostname, session) -> true);

                log.warn("SSL verification disabled for Prometheus client");
            } catch (Exception e) {
                log.error("Failed to disable SSL verification", e);
                throw new RuntimeException("Failed to configure SSL", e);
            }
        }

        this.httpClient = builder.build();
    }

    @Override
    public PrometheusToken getAuthToken() {
        String userId = properties.getAuth().getUserId();
        PrometheusToken token = getCachedToken(userId);

        // 检查token是否即将过期（5分钟内），如果是则主动刷新
        if (token != null && token.isExpiringSoon()) {
            log.info("Prometheus token is expiring soon, refreshing for user: {}", userId);
            evictCachedToken(userId);
            token = getCachedToken(userId);
        }

        return token;
    }

    /**
     * 获取缓存的Prometheus token
     * 使用Spring Cache进行分布式缓存，支持多用户
     *
     * @param userId 用户ID，用作缓存键
     * @return Prometheus token
     */
    @Cacheable(value = PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE, key = "#userId", unless = "#result == null")
    public PrometheusToken getCachedToken(String userId) {
        log.info("Fetching new Prometheus authentication token for user: {}", userId);
        return fetchNewToken();
    }

    /**
     * 清除指定用户的缓存token
     * 在token过期或认证失败时调用
     *
     * @param userId 用户ID
     */
    @CacheEvict(value = PrometheusCacheConfig.PROMETHEUS_TOKENS_CACHE, key = "#userId")
    public void evictCachedToken(String userId) {
        log.info("Evicting cached Prometheus token for user: {}", userId);
    }

    /**
     * 从认证服务器获取新的token
     */
    private PrometheusToken fetchNewToken() {
        try {
            // 构建请求对象
            PrometheusTokenRequest tokenRequest = PrometheusTokenRequest.builder()
                    .userId(properties.getAuth().getUserId())
                    .userKey(properties.getAuth().getUserKey())
                    .build();

            // 将请求对象转换为JSON
            String requestJson = objectMapper.writeValueAsString(tokenRequest);

            RequestBody requestBody = RequestBody.create(
                    requestJson,
                    MediaType.parse("application/json; charset=utf-8")
            );

            Request request = new Request.Builder()
                    .url(properties.getAuth().getTokenUrl())
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build();

            log.debug("Requesting Prometheus token from: {}", properties.getAuth().getTokenUrl());

            try (Response response = httpClient.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.debug("Token response received: HTTP {}, body: {}", response.code(), responseBody);

                if (!response.isSuccessful()) {
                    log.error("Failed to get Prometheus token, HTTP status: {}, body: {}",
                             response.code(), responseBody);
                    BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR);
                }

                return parseTokenResponse(responseBody);
            }
        } catch (IOException e) {
            log.error("Network error while fetching Prometheus token", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while fetching Prometheus token", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR);
        }
        return null;
    }

    /**
     * 解析token响应
     */
    private PrometheusToken parseTokenResponse(String responseBody) {
        try {
            PrometheusTokenResponse tokenResponse = objectMapper.readValue(responseBody, PrometheusTokenResponse.class);

            // 检查响应是否成功
            if (!tokenResponse.isSuccess()) {
                String errorMsg = String.format("Token request failed: %s - %s",
                                               tokenResponse.getMsgCd(),
                                               tokenResponse.getMsgInfo());
                log.error(errorMsg);

                // 根据不同的错误码抛出相应的异常
                if (tokenResponse.isAuthError()) {
                    BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR);
                } else if (tokenResponse.isUserStatusError()) {
                    BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR);
                } else {
                    BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE);
                }
            }

            String accessToken = tokenResponse.getToken();
            Long expiresIn = tokenResponse.getExpire();

            if (accessToken == null || accessToken.trim().isEmpty()) {
                log.error("Invalid token response: missing or empty token");
                BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE);
            }

            // 如果响应中没有expire，使用配置的默认值
            if (expiresIn == null || expiresIn <= 0) {
                expiresIn = (long) (properties.getAuth().getTokenCacheMinutes() * 60);
            }

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expiresAt = now.plusSeconds(expiresIn);

            log.info("Successfully obtained Prometheus token for user: {}, expires in {} seconds",
                    tokenResponse.getUserId(), expiresIn);

            return PrometheusToken.builder()
                    .accessToken(accessToken)
                    .tokenType("Bearer") // 固定使用Bearer类型
                    .createdAt(now)
                    .expiresAt(expiresAt)
                    .build();

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to parse token response: {}", responseBody, e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE);
        }
        return null;
    }

    @Override
    public PrometheusQueryResponse query(PrometheusQueryRequest request) {
        return executeQuery("/api/v1/query", request, null);
    }

    @Override
    public PrometheusQueryResponse queryRange(PrometheusQueryRequest request, long start, long end, String step) {
        var rangeParams = new java.util.HashMap<String, String>();
        rangeParams.put("start", String.valueOf(start));
        rangeParams.put("end", String.valueOf(end));
        rangeParams.put("step", step);

        return executeQuery("/api/v1/query_range", request, rangeParams);
    }

    /**
     * 执行Prometheus查询
     */
    private PrometheusQueryResponse executeQuery(String endpoint, PrometheusQueryRequest request,
                                               java.util.Map<String, String> additionalParams) {
        try {
            PrometheusToken token = getAuthToken();

            HttpUrl.Builder urlBuilder = HttpUrl.parse(properties.getBaseUrl() + endpoint).newBuilder()
                    .addQueryParameter("query", request.getQuery());

            // 添加时间参数
            if (request.getTimeUnix() != null) {
                urlBuilder.addQueryParameter("time", request.getTimeUnix().toString());
            }

            // 添加超时参数
            if (request.getTimeout() != null) {
                urlBuilder.addQueryParameter("timeout", request.getTimeout() + "s");
            }

            // 添加额外参数
            if (request.getAdditionalParams() != null) {
                request.getAdditionalParams().forEach(urlBuilder::addQueryParameter);
            }

            // 添加范围查询参数
            if (additionalParams != null) {
                additionalParams.forEach(urlBuilder::addQueryParameter);
            }

            Request httpRequest = new Request.Builder()
                    .url(urlBuilder.build())
                    .get()
                    .addHeader("Authorization", token.getAuthorizationHeader())
                    .build();

            log.debug("Executing Prometheus query: {}", httpRequest.url());

            try (Response response = httpClient.newCall(httpRequest).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";

                if (!response.isSuccessful()) {
                    log.error("Prometheus query failed, HTTP status: {}, body: {}",
                             response.code(), responseBody);

                    // 如果是401错误，可能是token过期，清除缓存的token
                    if (response.code() == 401) {
                        evictCachedToken(properties.getAuth().getUserId());
                        BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_TOKEN_EXPIRED);
                    }

                    BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_QUERY_ERROR);
                }

                log.debug("Prometheus query response: {}", responseBody);
                return parseQueryResponse(responseBody);
            }

        } catch (IOException e) {
            log.error("Network error during Prometheus query", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during Prometheus query", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_QUERY_ERROR);
        }
        return null;
    }

    /**
     * 解析查询响应
     */
    private PrometheusQueryResponse parseQueryResponse(String responseBody) {
        try {
            @SuppressWarnings("unchecked")
            var responseMap = objectMapper.readValue(responseBody, java.util.Map.class);

            String status = (String) responseMap.get("status");
            String error = (String) responseMap.get("error");
            String errorType = (String) responseMap.get("errorType");

            @SuppressWarnings("unchecked")
            var warnings = (java.util.List<String>) responseMap.get("warnings");

            PrometheusQueryResponse.PrometheusQueryResponseBuilder responseBuilder =
                    PrometheusQueryResponse.builder()
                            .status(status)
                            .error(error)
                            .errorType(errorType)
                            .warnings(warnings);

            // 解析数据部分
            @SuppressWarnings("unchecked")
            var dataMap = (java.util.Map<String, Object>) responseMap.get("data");
            if (dataMap != null) {
                String resultType = (String) dataMap.get("resultType");

                @SuppressWarnings("unchecked")
                var resultList = (java.util.List<java.util.Map<String, Object>>) dataMap.get("result");

                java.util.List<PrometheusQueryResponse.PrometheusResult> results = new java.util.ArrayList<>();
                if (resultList != null) {
                    for (var resultItem : resultList) {
                        @SuppressWarnings("unchecked")
                        var metric = (java.util.Map<String, String>) resultItem.get("metric");

                        @SuppressWarnings("unchecked")
                        var value = (java.util.List<Object>) resultItem.get("value");

                        @SuppressWarnings("unchecked")
                        var values = (java.util.List<java.util.List<Object>>) resultItem.get("values");

                        results.add(PrometheusQueryResponse.PrometheusResult.builder()
                                .metric(metric)
                                .value(value)
                                .values(values)
                                .build());
                    }
                }

                PrometheusQueryResponse.PrometheusData data = PrometheusQueryResponse.PrometheusData.builder()
                        .resultType(resultType)
                        .result(results)
                        .build();

                responseBuilder.data(data);
            }

            return responseBuilder.build();

        } catch (Exception e) {
            log.error("Failed to parse Prometheus query response: {}", responseBody, e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE);
        }
        return null;
    }

    @Override
    public IndicatorQueryResponse getIndicatorDataList(IndicatorQueryRequest request) {
        try {
            // 验证请求参数
            if (!validateIndicatorRequest(request)) {
                return IndicatorQueryResponse.builder()
                        .msgCd("FFM80003")
                        .msgInfo("请求参数验证失败")
                        .build();
            }

            PrometheusToken token = getAuthToken();

            // 构建请求URL
            String url = properties.getBaseUrl() + "/openability/v1/firefly/busOperation/getIndicatorDataList";

            // 构建请求体
            String requestJson = objectMapper.writeValueAsString(request);

            RequestBody requestBody = RequestBody.create(
                    requestJson,
                    MediaType.parse("application/json; charset=utf-8")
            );

            Request httpRequest = new Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", token.getAuthorizationHeader())
                    .build();

            log.debug("Executing indicator query: {}", url);
            log.debug("Request body: {}", requestJson);

            try (Response response = httpClient.newCall(httpRequest).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.debug("Indicator query response: HTTP {}, body: {}", response.code(), responseBody);

                if (!response.isSuccessful()) {
                    log.error("Indicator query failed, HTTP status: {}, body: {}",
                             response.code(), responseBody);

                    // 如果是401错误，可能是token过期，清除缓存的token
                    if (response.code() == 401) {
                        evictCachedToken(properties.getAuth().getUserId());
                        BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_TOKEN_EXPIRED);
                    }

                    BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_QUERY_ERROR);
                }

                return parseIndicatorQueryResponse(responseBody);
            }

        } catch (IOException e) {
            log.error("Network error during indicator query", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during indicator query", e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_QUERY_ERROR);
        }
        return null;
    }

    /**
     * 验证指标查询请求
     */
    private boolean validateIndicatorRequest(IndicatorQueryRequest request) {
        // 基本参数验证
        if (request.getStartTime() == null || request.getEndTime() == null) {
            log.error("Start time and end time are required");
            return false;
        }

        if (request.getIndicatorType() == null || request.getIndicatorType().trim().isEmpty()) {
            log.error("Indicator type is required");
            return false;
        }

        if (request.getIndicatorName() == null || request.getIndicatorName().trim().isEmpty()) {
            log.error("Indicator name is required");
            return false;
        }

        if (request.getPageNum() == null || request.getPageNum() < 1) {
            log.error("Page number must be greater than 0");
            return false;
        }

        if (request.getPageSize() == null || request.getPageSize() < 1 || request.getPageSize() > 3000) {
            log.error("Page size must be between 1 and 3000");
            return false;
        }

        // 时间间隔验证（不大于24小时）
        long timeDiff = java.time.Duration.between(request.getStartTime(), request.getEndTime()).toHours();
        if (timeDiff > 24) {
            log.error("Time interval cannot exceed 24 hours");
            return false;
        }

        // 使用元数据服务验证指标参数
        return metadataService.validateIndicatorParams(request.getIndicatorName(), request.getSpecialFields());
    }

    /**
     * 解析指标查询响应
     */
    private IndicatorQueryResponse parseIndicatorQueryResponse(String responseBody) {
        try {
            @SuppressWarnings("unchecked")
            var responseMap = objectMapper.readValue(responseBody, java.util.Map.class);

            String msgCd = (String) responseMap.get("msgCd");
            String msgInfo = (String) responseMap.get("msgInfo");

            IndicatorQueryResponse.IndicatorQueryResponseBuilder responseBuilder =
                    IndicatorQueryResponse.builder()
                            .msgCd(msgCd)
                            .msgInfo(msgInfo);

            // 如果成功，解析数据部分
            if ("FFM00000".equals(msgCd)) {
                @SuppressWarnings("unchecked")
                var dataList = (java.util.List<java.util.Map<String, Object>>) responseMap.get("data");

                if (dataList != null) {
                    java.util.List<IndicatorQueryResponse.IndicatorData> indicatorDataList =
                            new java.util.ArrayList<>();

                    for (var dataItem : dataList) {
                        IndicatorQueryResponse.IndicatorData indicatorData = parseIndicatorDataItem(dataItem);
                        if (indicatorData != null) {
                            indicatorDataList.add(indicatorData);
                        }
                    }

                    responseBuilder.data(indicatorDataList);
                }

                // 解析分页信息
                Object total = responseMap.get("total");
                Object pageNum = responseMap.get("pageNum");
                Object pageSize = responseMap.get("pageSize");

                if (total instanceof Number) {
                    responseBuilder.total(((Number) total).longValue());
                }
                if (pageNum instanceof Number) {
                    responseBuilder.pageNum(((Number) pageNum).intValue());
                }
                if (pageSize instanceof Number) {
                    responseBuilder.pageSize(((Number) pageSize).intValue());
                }
            }

            return responseBuilder.build();

        } catch (Exception e) {
            log.error("Failed to parse indicator query response: {}", responseBody, e);
            BusinessException.throwBusinessException(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE);
        }
        return null;
    }

    /**
     * 解析单个指标数据项
     */
    private IndicatorQueryResponse.IndicatorData parseIndicatorDataItem(java.util.Map<String, Object> dataItem) {
        try {
            IndicatorQueryResponse.IndicatorData.IndicatorDataBuilder builder =
                    IndicatorQueryResponse.IndicatorData.builder();

            // 解析基本字段
            builder.indicatorName((String) dataItem.get("indicatorName"));
            builder.value((String) dataItem.get("value"));
            builder.zone((String) dataItem.get("zone"));

            // 解析时间戳
            Object timestamp = dataItem.get("timestamp");
            if (timestamp instanceof String) {
                try {
                    builder.timestamp(java.time.LocalDateTime.parse((String) timestamp));
                } catch (Exception e) {
                    log.warn("Failed to parse timestamp: {}", timestamp);
                }
            }

            // 解析特殊字段
            builder.one((String) dataItem.get("one"));
            builder.two((String) dataItem.get("two"));
            builder.three((String) dataItem.get("three"));
            builder.four((String) dataItem.get("four"));
            builder.five((String) dataItem.get("five"));
            builder.six((String) dataItem.get("six"));
            builder.seven((String) dataItem.get("seven"));
            builder.eight((String) dataItem.get("eight"));
            builder.nine((String) dataItem.get("nine"));
            builder.ten((String) dataItem.get("ten"));

            // 解析动态字段
            java.util.Map<String, Object> dynamicFields = new java.util.HashMap<>();
            for (java.util.Map.Entry<String, Object> entry : dataItem.entrySet()) {
                String key = entry.getKey();
                if (!isStandardField(key)) {
                    dynamicFields.put(key, entry.getValue());
                }
            }
            builder.dynamicFields(dynamicFields);

            return builder.build();

        } catch (Exception e) {
            log.error("Failed to parse indicator data item: {}", dataItem, e);
            return null;
        }
    }

    /**
     * 检查是否为标准字段
     */
    private boolean isStandardField(String fieldName) {
        return java.util.Arrays.asList(
                "indicatorName", "timestamp", "value", "zone",
                "one", "two", "three", "four", "five",
                "six", "seven", "eight", "nine", "ten"
        ).contains(fieldName);
    }
}
