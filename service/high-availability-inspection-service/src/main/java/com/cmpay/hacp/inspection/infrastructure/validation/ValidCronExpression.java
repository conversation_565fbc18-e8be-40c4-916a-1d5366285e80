package com.cmpay.hacp.inspection.infrastructure.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CronExpressionValidator.class)
@Documented
public @interface ValidCronExpression {
    String message() default "无效的CRON表达式";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
