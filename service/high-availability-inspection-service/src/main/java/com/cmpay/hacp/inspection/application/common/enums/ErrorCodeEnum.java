package com.cmpay.hacp.inspection.application.common.enums;

import com.cmpay.lemon.common.AlertCapable;
import lombok.Getter;

@Getter
public enum ErrorCodeEnum implements AlertCapable {
    // 插件模块
    PLUGIN_ID_REQUIRED("HAI10001", "插件ID不能为空"),
    PLUGIN_UPDATE_FAILED("HAI10002", "更新插件失败"),
    // 规则模块
    RULE_ID_REQUIRED("HAI20001", "规则ID不能为空"),
    RULE_UPDATE_FAILED("HAI20002", "更新规则失败"),
    // 任务模块
    TASK_ID_REQUIRED("HAI30001", "任务ID不能为空"),
    TASK_CREATE_ERROR("HAI30002", "创建任务失败"),
    TASK_UPDATE_FAILED("HAI30003", "更新任务失败"),
    TASK_NOT_FOUND("HAI30004", "任务不存在"),
    TASK_EXECUTION_ERROR("HAI30005", "执行任务失败"),
    // 报告模块
    REPORT_NOT_FOUND("HAI40001", "报告不存在"),
    REPORT_DETAIL_NOT_FOUND("HAI40002", "报告详细内容不存在"),
    // Prometheus模块
    PROMETHEUS_CONNECTION_ERROR("HAI50001", "Prometheus连接失败"),
    PROMETHEUS_AUTH_ERROR("HAI50002", "Prometheus认证失败"),
    PROMETHEUS_QUERY_ERROR("HAI50003", "Prometheus查询失败"),
    PROMETHEUS_TOKEN_EXPIRED("HAI50004", "Prometheus认证令牌已过期"),
    PROMETHEUS_INVALID_RESPONSE("HAI50005", "Prometheus响应格式无效");

    private final String msgCd;
    private final String msgInfo;

    ErrorCodeEnum(String code, String message) {
        this.msgCd = code;
        this.msgInfo = message;
    }

    @Override
    public String getMsgCd() {
        return msgCd;
    }

    @Override
    public String getMsgInfo() {
        return msgInfo;
    }
}
