package com.cmpay.hacp.inspection.application.plugin;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;

import java.util.List;

/**
 * 指标元数据服务接口
 */
public interface IndicatorMetadataService {
    
    /**
     * 根据指标类型查询指标定义列表
     *
     * @param indicatorType 指标类型
     * @return 指标定义列表
     */
    List<IndicatorDefinition> getIndicatorDefinitionsByType(IndicatorType indicatorType);

    /**
     * 根据指标类型查询指标定义列表
     *
     * @param indicatorId 指标ID
     * @return 指标定义
     */
    IndicatorDefinition getIndicatorParam(String indicatorId);
}
