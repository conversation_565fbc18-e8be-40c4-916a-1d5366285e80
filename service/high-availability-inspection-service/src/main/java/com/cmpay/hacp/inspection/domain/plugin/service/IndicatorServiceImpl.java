package com.cmpay.hacp.inspection.domain.plugin.service;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.repository.IndicatorDefinitionRepository;
import com.cmpay.hacp.inspection.domain.plugin.repository.IndicatorParamRepository;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorServiceImpl implements IndicatorService {
    private final IndicatorDefinitionRepository indicatorDefinitionRepository;
    private final IndicatorParamRepository indicatorParamRepository;

    @Override
    public List<IndicatorDefinition> getIndicatorDefinitionsByType(IndicatorType indicatorType) {
        return indicatorDefinitionRepository.getIndicatorDefinitionsByType(indicatorType);
    }

    @Override
    public IndicatorDefinition getIndicatorDefinitionById(String indicatorId) {
        IndicatorDefinition indicatorDefinition = indicatorDefinitionRepository.getIndicatorDefinitionByID(indicatorId);
        return indicatorParamRepository.getIndicatorParams(indicatorDefinition);
    }
}
