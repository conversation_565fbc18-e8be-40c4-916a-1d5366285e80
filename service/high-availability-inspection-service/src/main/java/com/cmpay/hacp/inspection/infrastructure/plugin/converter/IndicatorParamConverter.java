package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PrometheusIndicatorParamDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface IndicatorParamConverter {
    List<IndicatorDefinition.IndicatorParam> toIndicatorParamList(List<PrometheusIndicatorParamDO> indicatorParamDOList);
}
