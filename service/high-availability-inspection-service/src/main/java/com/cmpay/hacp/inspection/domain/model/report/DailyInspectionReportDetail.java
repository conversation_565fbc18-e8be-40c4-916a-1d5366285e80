package com.cmpay.hacp.inspection.domain.model.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 按日巡检报告详细内容领域对象
 */
@Data
public class DailyInspectionReportDetail {

    /**
     * 报告ID
     */
    private String reportId;

    /**
     * 巡检分类统计（用于饼图展示）
     */
    private List<CategoryStatistics> categoryStatistics;

    /**
     * 近7日通过率趋势（用于柱状图展示）
     */
    private List<DailyPassRateTrend> recentPassRateTrends;

    /**
     * 异常详情列表（异常项标签页）
     */
    private ExceptionDetails exceptionDetails;

    /**
     * 趋势分析数据（趋势分析标签页）
     */
    private TrendAnalysis trendAnalysis;

    /**
     * 巡检分类统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryStatistics {
        /**
         * 分类名称（如：数据库、网络、应用、页面等）
         */
        private String categoryName;

        /**
         * 该分类的检查数量
         */
        private Integer checkCount;

        /**
         * 占比（百分比）
         */
        private Double percentage;
    }

    /**
     * 近7日通过率趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyPassRateTrend {
        /**
         * 日期
         */
        private LocalDate date;

        /**
         * 通过率
         */
        private Double passRate;

        /**
         * 通过率级别（用于颜色区分：≥95% 优秀、≥90% 良好、≥80% 中等、<80% 差）
         */
        private String level;
    }

    /**
     * 异常详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExceptionDetails {
        /**
         * 异常总数
         */
        private Integer totalExceptions;

        /**
         * 高优先级异常数
         */
        private Integer highPriorityExceptions;

        /**
         * 中优先级异常数
         */
        private Integer mediumPriorityExceptions;

        /**
         * 低优先级异常数
         */
        private Integer lowPriorityExceptions;

        /**
         * 异常详情列表
         */
        private List<ExceptionItem> exceptionItems;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExceptionItem {
            /**
             * 异常类型
             */
            private String exceptionType;

            /**
             * 异常描述
             */
            private String description;

            /**
             * 影响资源
             */
            private String affectedResources;

            /**
             * 优先级
             */
            private String priority;

            /**
             * 建议措施
             */
            private String suggestion;
        }
    }

    /**
     * 趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendAnalysis {
        /**
         * 通过率趋势数据
         */
        private List<TrendPoint> passRateTrend;

        /**
         * 响应时间趋势数据
         */
        private List<TrendPoint> responseTimeTrend;

        /**
         * 异常数量趋势数据
         */
        private ExceptionTrend exceptionTrend;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TrendPoint {
            /**
             * 日期
             */
            private LocalDate date;

            /**
             * 日期显示
             */
            private String dateDisplay;

            /**
             * 数值（通过率或响应时间）
             */
            private Double value;

            /**
             * 单位（%或ms）
             */
            private String unit;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExceptionTrend {
            /**
             * 错误趋势
             */
            private List<TrendPoint> errorTrend;

            /**
             * 警告趋势
             */
            private List<TrendPoint> warningTrend;
        }
    }
}
