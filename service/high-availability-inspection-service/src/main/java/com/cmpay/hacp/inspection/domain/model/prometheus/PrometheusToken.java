package com.cmpay.hacp.inspection.domain.model.prometheus;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Prometheus认证令牌值对象
 * 实现Serializable接口以支持分布式缓存
 */
@Data
@Builder
public class PrometheusToken implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 令牌类型（如：Bearer）
     */
    private String tokenType;
    
    /**
     * 令牌过期时间
     */
    private LocalDateTime expiresAt;
    
    /**
     * 令牌创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 检查令牌是否已过期
     * 
     * @return true如果令牌已过期，否则false
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }
    
    /**
     * 检查令牌是否即将过期（提前5分钟刷新）
     * 
     * @return true如果令牌即将过期，否则false
     */
    public boolean isExpiringSoon() {
        return LocalDateTime.now().plusMinutes(5).isAfter(expiresAt);
    }
    
    /**
     * 获取完整的Authorization头值
     * 
     * @return Authorization头值，格式为 "Bearer {token}"
     */
    public String getAuthorizationHeader() {
        return (tokenType != null ? tokenType : "Bearer") + " " + accessToken;
    }
}
