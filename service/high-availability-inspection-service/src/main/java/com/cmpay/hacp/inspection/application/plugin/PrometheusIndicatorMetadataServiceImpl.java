package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.PrometheusIndicatorMetadataService;
import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PrometheusIndicatorDefinitionDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PrometheusIndicatorParamDO;
import com.cmpay.hacp.inspection.infrastructure.database.repository.PrometheusIndicatorDefinitionRepository;
import com.cmpay.hacp.inspection.infrastructure.database.repository.PrometheusIndicatorParamRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Prometheus指标元数据服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrometheusIndicatorMetadataServiceImpl implements PrometheusIndicatorMetadataService {
    
    private final PrometheusIndicatorDefinitionRepository indicatorDefinitionRepository;
    private final PrometheusIndicatorParamRepository indicatorParamRepository;
    
    @Override
    public IndicatorDefinition getIndicatorDefinition(String indicatorEnglishName) {
        PrometheusIndicatorDefinitionDO definitionDO = indicatorDefinitionRepository
                .findByIndicatorEnglishName(indicatorEnglishName);
        
        if (definitionDO == null) {
            log.warn("Indicator definition not found for: {}", indicatorEnglishName);
            return null;
        }
        
        // 查询参数配置
        List<PrometheusIndicatorParamDO> paramDOs = indicatorParamRepository
                .findByIndicatorId(definitionDO.getIndicatorId());
        
        List<IndicatorDefinition.IndicatorParam> params = paramDOs.stream()
                .map(this::convertToIndicatorParam)
                .collect(Collectors.toList());
        
        return IndicatorDefinition.builder()
                .indicatorId(definitionDO.getIndicatorId())
                .indicatorName(definitionDO.getIndicatorName())
                .indicatorEnglishName(definitionDO.getIndicatorEnglishName())
                .groupCode(definitionDO.getGroupCode())
                .indicatorType(definitionDO.getIndicatorType())
                .description(definitionDO.getDescription())
                .unit(definitionDO.getUnit())
                .status(definitionDO.getStatus())
                .inputParams(params)
                .build();
    }
    
    @Override
    public List<IndicatorDefinition> getIndicatorDefinitionsByType(String indicatorType) {
        List<PrometheusIndicatorDefinitionDO> definitionDOs = indicatorDefinitionRepository
                .findByIndicatorType(indicatorType);
        
        return definitionDOs.stream()
                .map(this::convertToIndicatorDefinition)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<IndicatorDefinition> getIndicatorDefinitionsByGroup(String groupCode) {
        List<PrometheusIndicatorDefinitionDO> definitionDOs = indicatorDefinitionRepository
                .findByGroupCode(groupCode);
        
        return definitionDOs.stream()
                .map(this::convertToIndicatorDefinition)
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean validateIndicatorParams(String indicatorEnglishName, Map<String, String> specialFields) {
        IndicatorDefinition definition = getIndicatorDefinition(indicatorEnglishName);
        if (definition == null) {
            log.error("Indicator definition not found: {}", indicatorEnglishName);
            return false;
        }
        
        // 检查必填参数
        for (IndicatorDefinition.IndicatorParam param : definition.getInputParams()) {
            if (param.getIsRequired()) {
                String value = specialFields.get(param.getParamCode());
                if (value == null || value.trim().isEmpty()) {
                    log.error("Required parameter missing: {} for indicator: {}", 
                             param.getParamCode(), indicatorEnglishName);
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 转换为指标定义对象（不包含参数）
     */
    private IndicatorDefinition convertToIndicatorDefinition(PrometheusIndicatorDefinitionDO definitionDO) {
        return IndicatorDefinition.builder()
                .indicatorId(definitionDO.getIndicatorId())
                .indicatorName(definitionDO.getIndicatorName())
                .indicatorEnglishName(definitionDO.getIndicatorEnglishName())
                .groupCode(definitionDO.getGroupCode())
                .indicatorType(definitionDO.getIndicatorType())
                .description(definitionDO.getDescription())
                .unit(definitionDO.getUnit())
                .status(definitionDO.getStatus())
                .build();
    }
    
    /**
     * 转换为指标参数对象
     */
    private IndicatorDefinition.IndicatorParam convertToIndicatorParam(PrometheusIndicatorParamDO paramDO) {
        return IndicatorDefinition.IndicatorParam.builder()
                .paramOrder(paramDO.getParamOrder())
                .paramName(paramDO.getParamName())
                .paramCode(paramDO.getParamCode())
                .paramType(paramDO.getParamType())
                .isRequired(paramDO.getIsRequired() == 1)
                .defaultValue(paramDO.getDefaultValue())
                .description(paramDO.getDescription())
                .build();
    }
}
