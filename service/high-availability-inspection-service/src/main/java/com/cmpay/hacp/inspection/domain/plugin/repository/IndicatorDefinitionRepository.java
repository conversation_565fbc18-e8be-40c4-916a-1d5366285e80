package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.IndicatorDefinition;
import com.cmpay.hacp.inspection.domain.plugin.valueobject.IndicatorType;

import java.util.List;

public interface IndicatorDefinitionRepository {
    /**
     * 根据指标类型查询指标定义列表
     *
     * @param indicatorType 指标类型
     * @return 指标定义列表
     */
    List<IndicatorDefinition> getIndicatorDefinitionsByType(IndicatorType indicatorType);

    IndicatorDefinition getIndicatorDefinitionByID(String indicatorId);
}
