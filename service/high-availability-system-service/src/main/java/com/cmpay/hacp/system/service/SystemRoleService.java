package com.cmpay.hacp.system.service;

import com.cmpay.hacp.bo.menu.MenuBO;
import com.cmpay.hacp.bo.system.RoleBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR> tnw
 */
public interface SystemRoleService {

    /**
     * 查询所有角色
     *
     * @param userId
     * @return
     */
    List<RoleBO> getAllRoles(String userId);

    /**
     * 根据role id获取role对象
     *
     * @param roleId
     * @return
     */
    RoleBO getRole(Long roleId);

    /**
     * 根据role id获取所有的menus
     *
     * @param roleId
     * @return
     */
    List<MenuBO> getMenuIdsByRoleId(Long roleId);

    /**
     * 根据userId查找用户所有角色
     *
     * @param userId
     * @return
     */
    List<Long> getRolesByUserId(String userId);


    /**
     * 分页查询角色
     *
     * @param pageSize
     * @param pageNum
     * @param operatorId
     * @param roleBO
     * @return
     */
    PageInfo<RoleBO> getRolesPage(Integer pageSize, Integer pageNum, String operatorId, RoleBO roleBO);


    /**
     * 角色删除
     *
     * @param operatorId
     * @param roleId
     */
    void delete(String operatorId, Long roleId);

    /**
     * 角色批量删除
     *
     * @param operatorId
     * @param roleIds
     */
    void deleteBatch(String operatorId, List<Long> roleIds);


    /**
     * 根据role id 列表获取所有的menus
     *
     * @param roleIds
     * @return
     */
    List<MenuBO> getMenuIdsByRoleIds(List<Long> roleIds);

    /**
     * @param operatorId
     * @param roleBO
     * @param menuIds
     * @return
     */
    Long add(String operatorId, RoleBO roleBO, List<Long> menuIds);

    /**
     * @param operatorId
     * @param roleId
     * @param menuIds
     */
    void addRoleMenu(String operatorId, Long roleId, List<Long> menuIds);

    /**
     * @param operatorId
     * @param roleBO
     * @param menuIds
     * @return
     */
    Long update(String operatorId, RoleBO roleBO, List<Long> menuIds);


}
