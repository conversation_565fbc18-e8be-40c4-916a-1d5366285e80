package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.system.bo.system.LoginHistoryLogBO;
import com.cmpay.hacp.system.bo.system.LoginLatestInfoBO;
import com.cmpay.hacp.system.dao.ILoginHistoryLogExtDao;
import com.cmpay.hacp.system.entity.LoginHistoryLogDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemLoginHistoryLogService;
import com.cmpay.hacp.system.service.SystemLoginLatestInfoService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Primary
public class SystemLoginHistoryLogServiceImpl implements SystemLoginHistoryLogService {

    private final static Logger LOGGER = LoggerFactory.getLogger(SystemLoginHistoryLogServiceImpl.class);

    @Resource
    private ILoginHistoryLogExtDao loginHistoryLogExtDao;

    @Autowired
    private SystemLoginLatestInfoService systemLoginLatestInfoService;

    @Value("${server.port:8080}")
    private String port;

    @Override
    public void addLoginHistoryLog(LoginHistoryLogBO loginHistoryLog) {
        LoginHistoryLogDO loginHistoryLogDO = new LoginHistoryLogDO();
        String id = UUID.randomUUID().toString().replace("-", "").toUpperCase();
        BeanUtils.copyProperties(loginHistoryLogDO, loginHistoryLog);
        loginHistoryLogDO.setId(id);
        int insert = loginHistoryLogExtDao.insert(loginHistoryLogDO);
        if (insert < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
        LoginLatestInfoBO loginLatestInfo = new LoginLatestInfoBO();
        loginLatestInfo.setPid(loginHistoryLogDO.getId());
        loginLatestInfo.setUserId(loginHistoryLogDO.getUserId());
        loginLatestInfo.setLatestDate(loginHistoryLogDO.getLoginDate());
        loginLatestInfo.setLatestTime(loginHistoryLogDO.getLoginTime());
        systemLoginLatestInfoService.addLoginLatestInfo(loginLatestInfo);

    }

    @Override
    public PageInfo<LoginHistoryLogBO> queryPageLoginHistoryLog(LoginHistoryLogBO loginHistoryLog, int pageNum, int pageSize) {
        PageInfo<LoginHistoryLogBO> pageInfo = null;
        LoginHistoryLogDO loginHistoryLogDO = new LoginHistoryLogDO();
        BeanUtils.copyProperties(loginHistoryLogDO, loginHistoryLog);
        if (pageNum == 0 || pageSize == 0) {
            pageInfo = new PageInfo<>(BeanConvertUtil.convertList(loginHistoryLogExtDao.queryLoginHistoryLog(loginHistoryLogDO,
                    loginHistoryLog.getBeginDate(),
                    loginHistoryLog.getEndDate()), LoginHistoryLogBO.class));
        } else {
            pageInfo = PageUtils.pageQueryWithCount(pageNum,
                    pageSize,
                    () -> BeanConvertUtil.convertList(loginHistoryLogExtDao.queryLoginHistoryLog(loginHistoryLogDO,
                            loginHistoryLog.getBeginDate(),
                            loginHistoryLog.getEndDate()), LoginHistoryLogBO.class));
        }
        return pageInfo;
    }
}
