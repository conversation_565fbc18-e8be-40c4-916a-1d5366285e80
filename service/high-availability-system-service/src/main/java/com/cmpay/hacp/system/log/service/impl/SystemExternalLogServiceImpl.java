package com.cmpay.hacp.system.log.service.impl;

import com.cmpay.hacp.system.log.bo.ExternalLogBO;
import com.cmpay.hacp.system.log.service.ExternalLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * <p>
 * 不对接4A日志上传的实现
 */
@Service
public class SystemExternalLogServiceImpl implements ExternalLogService {

    private final Logger logger = LoggerFactory.getLogger(SystemExternalLogServiceImpl.class);

    @Override
    @Async
    public void sendLog(ExternalLogBO syslogSendTo4ABO) {
    }
}
