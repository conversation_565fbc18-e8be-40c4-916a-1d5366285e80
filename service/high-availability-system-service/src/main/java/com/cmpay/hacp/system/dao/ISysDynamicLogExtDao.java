/*
 * @ClassName ISysDynamicLogDao
 * @Description
 * @version 1.0
 * @Date 2024-03-25 09:35:14
 */
package com.cmpay.hacp.system.dao;

import com.cmpay.hacp.system.log.bo.SysDynamicLogBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ISysDynamicLogExtDao extends ISysDynamicLogDao {
    /**
     * 查询动态日志列表
     *
     * @param requestId
     * @return
     */
    List<SysDynamicLogBO> getDynamicListByRequestId(@Param("requestId") String requestId);

    /**
     * 更新响应数据大小
     *
     * @param dataSize        响应数据大小
     * @param dataSizeType    响应数据大小类型
     * @param requestId       日志ID
     * @param interfaceRecord 调用接口
     * @return
     */
    long updateDynamicRspDataSize(@Param("dataSize") Long dataSize, @Param("dataSizeType") String dataSizeType, @Param("requestId") String requestId, @Param("interfaceRecord") String interfaceRecord);
}

