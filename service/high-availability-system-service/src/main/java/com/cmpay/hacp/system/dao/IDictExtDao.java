
package com.cmpay.hacp.system.dao;

import com.cmpay.hacp.system.entity.DictDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IDictExtDao extends IDictDao {

    /**
     * 查询字典列表
     * 可模糊查询
     *
     * @param dictDO
     * @return
     */
    List<DictDO> queryListDict(DictDO dictDO);

    /**
     * 批量删除字段列表
     *
     * @param ids
     * @return
     */
    long deleteBatch(@Param("ids") List<String> ids);

    /**
     * 批量删除字段列表
     *
     * @param parentIds
     * @return
     */
    long deleteBatchByParentId(@Param("parentIds") List<String> parentIds);

    /**
     * 更新类型标识根据父ID
     *
     * @param dictDO
     * @return
     */
    int updateTypeByParentId(DictDO dictDO);

    List<DictDO> queryChildren(DictDO dictDO);

    List<DictDO> listAllByType(@Param("types") String... types);
}
