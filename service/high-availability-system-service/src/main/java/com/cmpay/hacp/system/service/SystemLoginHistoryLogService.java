package com.cmpay.hacp.system.service;

import com.cmpay.hacp.system.bo.system.LoginHistoryLogBO;
import com.cmpay.lemon.framework.page.PageInfo;

/**
 * <AUTHOR>
 */
public interface SystemLoginHistoryLogService {

    /**
     * 登录登录历史详情
     *
     * @param loginHistoryLog
     */
    void addLoginHistoryLog(LoginHistoryLogBO loginHistoryLog);

    /**
     * 分页查询登录历史
     *
     * @param loginHistoryLog
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageInfo<LoginHistoryLogBO> queryPageLoginHistoryLog(LoginHistoryLogBO loginHistoryLog, int pageNum, int pageSize);
}
