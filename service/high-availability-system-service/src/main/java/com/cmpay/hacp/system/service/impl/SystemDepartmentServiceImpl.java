package com.cmpay.hacp.system.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.cmpay.hacp.bo.system.DeptBO;
import com.cmpay.hacp.bo.system.RoleBO;
import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.system.dao.IDeptExtDao;
import com.cmpay.hacp.system.dao.ISystemDepartmentExtDao;
import com.cmpay.hacp.system.dao.IUserExtDao;
import com.cmpay.hacp.system.dao.IUserRoleExtDao;
import com.cmpay.hacp.system.entity.DeptDO;
import com.cmpay.hacp.system.entity.RoleDO;
import com.cmpay.hacp.system.entity.UserDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemDepartmentService;
import com.cmpay.hacp.system.service.SystemDictionaryService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hacp.utils.reflection.Reflection;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SystemDepartmentServiceImpl implements SystemDepartmentService {

    private static final Logger logger = LoggerFactory.getLogger(SystemDepartmentServiceImpl.class);

    @Resource
    private IDeptExtDao deptExtDao;
    @Resource
    private IUserExtDao userExtDao;
    @Resource
    private IUserRoleExtDao userRoleExtDao;
    @Autowired
    private SystemDictionaryService systemDictionaryService;
    @Resource
    private ISystemDepartmentExtDao systemDepartmentExtDao;

    @Resource(name = "hacpObjectMapper")
    private ObjectMapper objectMapper;

    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public List<DeptBO> getUserDepartments(String operatorId) {
        //查询所有管理员
        List<DeptBO> deptBOList = this.getAllDepartments(operatorId);
        //是否是管理员
        if (!systemDictionaryService.isAdmin(operatorId, applicationName)) {
            //计算出用户所在部门及下属部门ID
            List<String> deptIds = this.getDeptIds(operatorId);
            //查询符合条件的下属部门详情
            List<DeptDO> childDepartments = deptExtDao.queryChildDepartments(new DeptDO(), deptIds);
            //转为树形结构
            deptBOList = this.getDeptTree(childDepartments, deptIds.get(0));
        }
        return deptBOList;
    }

    @Override
    public List<DeptBO> getAllDepartments(String operatorId) {
        List<DeptBO> deptBOList = new ArrayList<>();
        DeptDO deptDO = new DeptDO();
        List<DeptDO> allDepts = deptExtDao.queryDepartments(deptDO);
        if (JudgeUtils.isEmpty(allDepts)) {
            return deptBOList;
        }
        return this.getDeptTree(allDepts, "0");
    }

    @Override
    public DeptBO getDepartmentInfo(String operatorId, String deptId) {
        DeptDO deptDO = deptExtDao.get(deptId);
        DeptBO deptBO = new DeptBO();
        if (JudgeUtils.isNotNull(deptDO)) {
            BeanUtils.copyProperties(deptBO, deptDO);
        }
        return deptBO;

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void add(String operatorId, DeptBO deptBO) {
        DeptDO deptDO = new DeptDO();
        BeanUtils.copyProperties(deptDO, deptBO);
        //是否是管理员
        if ("0".equals(deptBO.getParentId())&&!systemDictionaryService.isAdmin(operatorId, applicationName)) {
            UserBO userInfo = getUserInfo(operatorId);
            deptDO.setParentId(userInfo.getDeptId());
        }
        if(JudgeUtils.isBlank(deptBO.getDeptId())){
            deptDO.setDeptId(IdGenUtil.randomUUID());
        }
        int insert = deptExtDao.insert(deptDO);
        if (insert < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void deleteBatch(List<String> deptIds) {
        deptIds.stream().forEach(this::delete);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void delete(String deptId) {
        DeptDO deptDO = new DeptDO();
        deptDO.setParentId(deptId);
        List<DeptDO> deptDOS = deptExtDao.find(deptDO);
        if (JudgeUtils.isNotEmpty(deptDOS)) {
            BusinessException.throwBusinessException(MsgEnum.DELETE_SUBMENU_OR_BUTTON_FIRST);
        }
        deptExtDao.delete(deptId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void update(String operatorId, DeptBO deptBO) {
        DeptDO deptDO = new DeptDO();
        BeanUtils.copyProperties(deptDO, deptBO);
        int update = deptExtDao.update(deptDO);
        if (update < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
    }

    @Override
    public List<String> getDeptIds(String userId) {
        UserBO userInfo = getUserInfo(userId);
        if (JudgeUtils.isNull(userInfo) || JudgeUtils.isBlank(userInfo.getDeptId())) {
            return new ArrayList<>();
        }
        //用户所在部门ID
        String deptId = userInfo.getDeptId();
        // 用户所在部门及子部门所在ID集合
        List<String> deptIds = new ArrayList<>();
        // 查询全量树形结构菜单
        List<DeptBO> departmentList = this.getAllDepartments(userId);
        deptIds.add(deptId);
        //计算子部门id, 从树形结构上找到子节点后，取出其中所在区域，遍历所有节点id
        this.getDeptIds(deptIds, deptId, departmentList);
        return deptIds;
    }


    /**
     * 计算下属部门Id
     *
     * @param deptIds  存放结果集合
     * @param parentId 上级部门id
     * @param depts    全量菜单树形结构
     * @return
     */
    private void getDeptIds(List<String> deptIds, String parentId, List<DeptBO> depts) {
        for (int i = 0; i < depts.size(); i++) {
            DeptBO deptBO = depts.get(i);
            //如果是子部门
            if (JudgeUtils.equalsIgnoreCase(deptBO.getParentId(), parentId)) {
                if (!deptIds.contains(deptBO.getDeptId())) {
                    deptIds.add(deptBO.getDeptId());
                }
                //如果存在下级继续查找
                if (JudgeUtils.isNotEmpty(deptBO.getChildrenDeptList())) {
                    getDeptIds(deptIds, deptBO.getDeptId(), deptBO.getChildrenDeptList());
                    continue;
                }
            }
            //如果存在下级继续查找
            if (JudgeUtils.isNotEmpty(deptBO.getChildrenDeptList())) {
                getDeptIds(deptIds, parentId, deptBO.getChildrenDeptList());
            }
        }
    }

    @Override
    public UserBO getUserInfo(String userId) {
        UserBO userBO = new UserBO();
        UserDO userDO = userExtDao.get(userId);
        if (JudgeUtils.isNull(userDO)) {
            return userBO;
        }
        BeanUtils.copyProperties(userBO, userDO);
        userBO.setPassword(null);
        List<RoleDO> roleDOS = userRoleExtDao.getRolesByUserId(userId);
        if (JudgeUtils.isNotEmpty(roleDOS)) {
            userBO.setRoleList(BeanConvertUtil.convertList(roleDOS, RoleBO.class));
        }
        return userBO;
    }

    private List<DeptBO> getDeptTree(List<DeptDO> depts, String parentId) {
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("deptId");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setWeightKey("orderNum");
        treeNodeConfig.setChildrenKey("childrenDeptList");
        //转换器
        DeptBO parentDeptBO = new DeptBO();
        List<DeptBO> childrenDeptList = new ArrayList<>();
        List<Tree<String>> treeDicts = TreeUtil.build(depts, parentId, treeNodeConfig,
                (dept, tree) -> {
                    if (JudgeUtils.isNull(dept.getOrderNum())) {
                        dept.setOrderNum(0);
                    }
                    // 扩展属性 ...
                    Reflection.reflectionTree(dept, tree);
                    if (JudgeUtils.equalsIgnoreCase(dept.getDeptId(), parentId)) {
                        BeanUtils.copyProperties(parentDeptBO, dept);
                    }
                });

        try {
            String treeJson = objectMapper.writeValueAsString(treeDicts);
            childrenDeptList = objectMapper.readValue(treeJson, new TypeReference<List<DeptBO>>() {
            });
        } catch (JsonProcessingException e) {
            logger.warn("getDeptTree JsonProcessingException {}", e.getMessage());
        }
        if (JudgeUtils.isNotNull(parentDeptBO) && JudgeUtils.isNotEmpty(parentDeptBO.getDeptName())) {
            List<DeptBO> parentDeptList = new ArrayList<>();
            parentDeptBO.setChildrenDeptList(childrenDeptList);
            parentDeptList.add(parentDeptBO);
            return parentDeptList;
        } else {
            return childrenDeptList;
        }
    }

    @Override
    @Cacheable(cacheNames = "SYSTEM_USER", key = "'AUTH:DEPT'+ #deptName")
    public String findDeptId(String deptName) {
        List<String> deptId = systemDepartmentExtDao.findDeptId(deptName);
        if (JudgeUtils.isEmpty(deptId))
            return null;
        return deptId.get(0);
    }

    @Override
    public List<String> getAllDeptIds() {
        List<String> deptIds = deptExtDao.getAllDeptIds();
        if (JudgeUtils.isEmpty(deptIds)) {
            return new ArrayList<>();
        }
        return deptIds;
    }

}
