<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IAppExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.AppDO">
        <id column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="app_owner_id" property="appOwnerId" jdbcType="VARCHAR"/>
        <result column="app_key" property="appKey" jdbcType="VARCHAR"/>
        <result column="salt" property="salt" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="token_key" property="tokenKey" jdbcType="VARCHAR"/>
        <result column="user_source" property="userSource" jdbcType="VARCHAR"/>
        <result column="sm_public_key" property="smPublicKey" jdbcType="VARCHAR"/>
        <result column="sm_private_key" property="smPrivateKey" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.system.entity.AppDO" extends="BaseResultMap">
        <result column="pub_key" property="pubKey" jdbcType="LONGVARCHAR"/>
        <result column="pri_key" property="priKey" jdbcType="LONGVARCHAR"/>
    </resultMap>
</mapper>
