package com.cmpay.hacp.dashboard.grafana;

import com.cmpay.hacp.system.bo.system.DictBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.dashboard.DashboardProperties;
import com.cmpay.hacp.dispatch.bo.ApiLocationBO;
import com.cmpay.hacp.dispatch.bo.DispatchBO;
import com.cmpay.hacp.dispatch.bo.DispatchZoneBO;
import com.cmpay.hacp.dispatch.service.ApiLocationService;
import com.cmpay.hacp.dispatch.service.DispatchService;
import com.cmpay.hacp.dispatch.service.DispatchZoneService;
import com.cmpay.hacp.system.service.SystemDictionaryService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.cmpay.hacp.dashboard.grafana.GrafanaTemplates.DEFAULT_TEMPLATE_NAME;

@Slf4j
@Component
public class GrafanaHelper {
    public static final String DEFAULT_DASHBOARD_NAME = "HAFR-Default";
    private static final TypeReference<Map<String, Object>> TYPE_REF = new TypeReference<Map<String, Object>>() {
    };
    private final DashboardProperties properties;
    private final GrafanaClient client;
    private final GrafanaTemplates templates;
    private final ObjectMapper objectMapper;

    private final DispatchZoneService dispatchZoneService;

    private final ApiLocationService apiLocationService;

    private final DispatchService dispatchService;

    private SystemDictionaryService systemDictionaryService;

    private Map<String,String> overrideFieldNameMap = new HashMap<>();

    private final ExpressionParser expressionParser;

    private final String DISPATCH_OVERRIDER_DICT_TYPE="dispatchOverrider";

    public GrafanaHelper(DashboardProperties properties, GrafanaClient client, GrafanaTemplates templates,
            DispatchZoneService dispatchZoneService, ApiLocationService apiLocationService, DispatchService dispatchService,SystemDictionaryService systemDictionaryService) {
        this.properties = properties;
        this.client = client;
        this.templates = templates;
        this.objectMapper = new ObjectMapper();
        this.expressionParser = new SpelExpressionParser();
        this.dispatchZoneService = dispatchZoneService;
        this.apiLocationService = apiLocationService;
        this.dispatchService = dispatchService;
        this.systemDictionaryService = systemDictionaryService;
    }

    private String getDatasource() {
        String datasourceName = properties.getGrafana().getDatasourceName();
        Map<String, Object> datasource = client.getDatasource(datasourceName);
        if (datasource == null || !datasource.containsKey("uid")) {
            BusinessException.throwBusinessException("HAC10020");
        }
        log.debug("Detected datasource: {}", datasource);
        return (String) datasource.get("uid");
    }

    private Map<String, String> genTemplateData(String dashboardName, String datasourceUID) {
        Map<String, String> data = new HashMap<>();
        data.put("dashboard.name", dashboardName);
        data.put("datasource.uid", datasourceUID);
        data.putAll(overrideFieldNameMap);
        return data;
    }

    public Map<String, Object> prepareDashboard(String workspaceId) {
        generateMappingFieldJson(workspaceId);
        return prepareDashboard(DEFAULT_DASHBOARD_NAME + "-" + workspaceId, DEFAULT_TEMPLATE_NAME);
    }

    private void generateMappingFieldJson(String workspaceId) {
        List<DispatchZoneBO> simpleList = dispatchZoneService.getSimpleList(workspaceId);
        String panelOverridesJson = CommonConstant.PANEL_OVERRIDE_JSON;
        EvaluationContext ctx = new StandardEvaluationContext();
        List<String> strs = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(simpleList)) {
            simpleList.forEach(f -> {
                ctx.setVariable("name", "机房：" + f.getZoneLabel());
                ctx.setVariable("cn", "机房：" + f.getZoneName());
                String value = (String) expressionParser.parseExpression(panelOverridesJson, new TemplateParserContext()).getValue(ctx);
                strs.add(value);
            });
            overrideFieldNameMap.put("overrides.zone", String.join(",", strs));
        }
        strs.clear();
        List<ApiLocationBO> apiLocationList = apiLocationService.getApiLocationList(workspaceId);
        if (JudgeUtils.isNotEmpty(apiLocationList)) {
            apiLocationList.forEach(f -> {
                if (JudgeUtils.isEmpty(simpleList)) {
                    return;
                }
                simpleList.forEach(zone -> {
                    ctx.setVariable("name", "机房" + zone.getZoneLabel() + "：" + f.getApiLocationName());
                    ctx.setVariable("cn", "机房" + zone.getZoneName() + "：" + f.getApiLocationCn());
                    String value = (String) expressionParser.parseExpression(panelOverridesJson, new TemplateParserContext()).getValue(ctx);
                    strs.add(value);
                });
            });
            overrideFieldNameMap.put("overrides.interface", String.join(",", strs));
        }
        List<DispatchBO> dispatchList = dispatchService.getDispatchList(workspaceId);
        strs.clear();
        if (JudgeUtils.isNotEmpty(dispatchList)) {
            dispatchList.forEach(f -> {
                if (JudgeUtils.isEmpty(simpleList)) {
                    return;
                }
                simpleList.forEach(zone -> {
                    ctx.setVariable("name", "机房" + zone.getZoneLabel() + "：" + f.getDispatchName());
                    ctx.setVariable("cn", "机房" + zone.getZoneName() + "：" + f.getDispatchCn());
                    String value = (String) expressionParser.parseExpression(panelOverridesJson, new TemplateParserContext()).getValue(ctx);
                    strs.add(value);
                });
            });
            overrideFieldNameMap.put("overrides.dispatch", String.join(",", strs));
        }
        DictBO dictBO = new DictBO();
        dictBO.setType(DISPATCH_OVERRIDER_DICT_TYPE);
        List<DictBO> dictList = systemDictionaryService.queryDictChildren(dictBO);
        strs.clear();
        if(JudgeUtils.isNotEmpty(dictList)){
            dictList.forEach(f->{
                ctx.setVariable("name", "MEM: " + f.getValue());
                ctx.setVariable("cn", "MEM: "+f.getLabel());
                String value = (String) expressionParser.parseExpression(panelOverridesJson, new TemplateParserContext()).getValue(ctx);
                strs.add(value);
                ctx.setVariable("name", "CPU: " + f.getValue());
                ctx.setVariable("cn", "CPU: "+f.getLabel());
                value = (String) expressionParser.parseExpression(panelOverridesJson, new TemplateParserContext()).getValue(ctx);
                strs.add(value);
            });
            overrideFieldNameMap.put("overrides.instance", JudgeUtils.isEmpty(strs) ? "":CommonConstant.COMMA+String.join(",", strs));
        }
    }

    public Map<String, Object> prepareDashboard(String dashboardName, String templateName) {
        log.info("Create dashboard: {}", dashboardName);
        String datasourceUID = getDatasource();
        Map<String, Object> dashboard = client.searchDashboardByName(dashboardName, null);
        if (dashboard == null) {
            Map<String, String> templateData = genTemplateData(dashboardName, datasourceUID);
            dashboard = doCreate(templateName, templateData, null);
        } else {
            log.debug("Dashboard {} already exists, skip creating", dashboardName);
        }
        return dashboard;
    }

    private Map<String, Object> doCreate(String templateName, Map<String, String> templateData, String parentUID) {
        String template = templates.render(templateName, templateData);
        try {
            Map<String, Object> dashboard = objectMapper.readValue(template, TYPE_REF);
            Map<String, Object> info = client.createDashboard(dashboard, parentUID);
            log.info("Created dashboard: {}", info);
            return info;
        } catch (JsonProcessingException e) {
            log.error("Deserialize dashboard data error, template name: {}", templateName, e);
        }
        return null;
    }

    public List<GrafanaPanel> dashboardPanels(String dashboardName) {
        Map<String, Object> dashboard = client.searchDashboardByName(dashboardName, null);
        if (dashboard == null) {
            return null;
        }
        return dashboardPanelsByUID((String) dashboard.get("uid"));
    }

    public List<GrafanaPanel> dashboardPanelsByUID(String UID) {
        Map<String, Object> res = client.getDashboardByUID(UID);
        Object dashboard = res.get("dashboard");
        if (!(dashboard instanceof Map)) {
            log.warn("No dashboard found: {}", res);
            return null;
        }
        Object panels = ((Map<String, Object>) dashboard).get("panels");
        if (!(panels instanceof List)) {
            log.warn("No panels found in dashboard: {}", dashboard);
            return null;
        }
        List<GrafanaPanel> result = new ArrayList<>();
        for (Object panel : (List) panels) {
            if (!(panel instanceof Map)) {
                continue;
            }
            try {
                GrafanaPanel p = new GrafanaPanel((Map<String, Object>) panel);
                result.add(p);
            } catch (Exception e) {
                log.warn("Parse panel error: {}", panel, e);
            }
        }
        return result;
    }
}
