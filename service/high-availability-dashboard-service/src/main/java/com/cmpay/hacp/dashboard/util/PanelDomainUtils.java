package com.cmpay.hacp.dashboard.util;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class PanelDomainUtils {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static Map<String, Object> deserializeOptions(String options) {
        if (StringUtils.isBlank(options)) {
            return new HashMap<>();
        }
        try {
            return OBJECT_MAPPER.readValue(options, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.error("Deserialize options failed", e);
            return new HashMap<>();
        }
    }

    public static String serializeOptions(Map<String, Object> options) {
        if (JudgeUtils.isEmpty(options)) {
            return "";
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(options);
        } catch (Exception e) {
            log.error("Serialize options failed", e);
            return "";
        }
    }
}
