package com.cmpay.hacp.dashboard.grafana;

import com.cmpay.hacp.dashboard.bo.PanelType;
import lombok.Data;

import java.util.EnumMap;
import java.util.Map;

@Data
public class WorkspaceDashboards {
    private final String workspaceId;
    private final EnumMap<PanelType, Map<String, Object>> info = new EnumMap<>(PanelType.class);

    public WorkspaceDashboards(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public void put(PanelType type, Map<String, Object> info) {
        this.info.put(type, info);
    }

    public Map<String, Object> getPanelInfo(PanelType type) {
        return info.get(type);
    }

    public String getPanelUID(PanelType type) {
        Map<String, Object> info = getPanelInfo(type);
        if (info == null || !info.containsKey("uid")) {
            return null;
        }
        return (String) info.get("uid");
    }

    public String getPanelURL(PanelType type) {
        Map<String, Object> info = getPanelInfo(type);
        if (info == null || !info.containsKey("url")) {
            return null;
        }
        return (String) info.get("url");
    }
}
